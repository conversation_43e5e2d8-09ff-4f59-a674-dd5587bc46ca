<template>
  <div class="app-container scroll-y">
    <el-alert title="请您选择酒店：" type="warning" :closable="false"></el-alert>
    <br />
    <el-form :model="queryParams" size="small" ref="queryForm" :inline="true">
      <el-form-item label="酒店名称" prop="hotelName">
        <el-input
          v-model="queryParams.hotelName"
          placeholder="请输入酒店名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="酒店编号" prop="hotelCode">
        <el-input
          v-model="queryParams.hotelCode"
          placeholder="请输入酒店编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.hotel_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="hotelList"
      highlight-current-row
    >
      <el-table-column label="酒店编码" align="center" prop="hotelCode" />
      <el-table-column label="酒店名称" align="center" prop="hotelName" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.hotel_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="酒店地址" align="center" prop="detailAddress" />
      <el-table-column label="联系电话" align="center" prop="telephone" />
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleRowClick(scope.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />
  </div>
</template>

<script>
import { getHotelInfoList } from '@/api/hotel/basic';

export default {
  name: 'HotelSelector',
  dicts: ['hotel_status'],
  data() {
    return {
      loading: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hotelName: undefined,
        hotelCode: undefined,
        status: '-1',
      },
      total: 0,
      hotelList: [],
      selectedHotel: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      getHotelInfoList(this.queryParams).then((response) => {
        this.hotelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleRowClick(row) {
      this.selectedHotel = row;
      this.$emit('selected', row);
    },
  },
};
</script>
<style lang="scss" scoped></style>
