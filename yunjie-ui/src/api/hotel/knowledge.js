// 知识库接口
import request from "@/utils/request"; 

// 新增知识库
export function addKnowledge(data) {
  return request({
    url: "/hotelKnowledge/add",
    method: "post",
    data
  });
}

// 删除知识库
export function deleteKnowledge(ids) {
  return request({
    url: "/hotelKnowledge/delete",
    method: "post",
    data: {
      ids: [].concat(ids)
      // 这里如果有其他参数，可按需添加
    }
  });
}

// 修改知识库信息
export function updateKnowledge(data) {
  return request({
    url: "/hotelKnowledge/update",
    method: "post",
    data
  });
}

// 获取知识库列表
export function getKnowledgeList(data) {
  return request({
    url: "/hotelKnowledge/getHotelKnowledgeList",
    method: "post",
    data
  });
}

// 获取知识库详情信息
export function getKnowledgeDetail(id) {
  return request({
    url: "/hotelKnowledge/getHotelKnowledge",
    method: "post",
    data: {
      id
    }
  });
}

// 修改知识库状态
export function updateKnowledgeStatus(id, status) {
  return request({
    url: "/hotelKnowledge/changeStatus",
    method: "post",
    data: {
      id,
      status
    }
  });
}
