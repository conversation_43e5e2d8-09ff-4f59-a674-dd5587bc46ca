//广告信息
import request from "@/utils/request";

// 新增广告
export function addAd(data) {
  return request({
    url: "/hotelAd/add",
    method: "post",
    data
  });
}

// 删除广告
export function deleteAd(ids) {
  return request({
    url: "/hotelAd/delete",
    method: "post",
    data: {
      ids: [].concat(ids)
      // 这里如果有其他参数，可按需添加
    }
  });
}

// 修改广告信息
export function updateAd(data) {
  return request({
    url: "/hotelAd/update",
    method: "post",
    data
  });
}

// 获取广告列表
export function getAdList(data) {
  return request({
    url: "/hotelAd/getHotelAdList",
    method: "post",
    data
  });
}

// 获取广告详情信息
export function getAdDetail(id) {
  return request({
    url: "/hotelAd/getHotelAd",
    method: "post",
    data: {
      id
    }
  });
}

// 修改广告状态
export function updateAdStatus(id,status) {
  return request({
    url: "/hotelAd/changeStatus",
    method: "post",
    data:{
      id,
      status
    }
  });
}
