//渠道信息
import request from "@/utils/request"; 

// 新增渠道
export function addChannel(data) {
  return request({
    url: "/hotelChannel/add",
    method: "post",
    data
  });
}

// 删除渠道
export function deleteChannel(ids) {
  return request({
    url: "/hotelChannel/delete",
    method: "post",
    data: {
      ids: [].concat(ids)
    }
  });
}

// 修改渠道信息
export function updateChannel(data) {
  return request({
    url: "/hotelChannel/update",
    method: "post",
    data
  });
}

// 获取渠道列表
export function getChannelList(data) {
  return request({
    url: "/hotelChannel/getHotelChannelList",
    method: "post",
    data
  });
}

// 获取渠道详情信息
export function getChannelDetail(id) {
  return request({
    url: "/hotelChannel/getHotelChannel",
    method: "post",
    data: {
      id
    }
  });
}

// 修改渠道状态
export function updateChannelStatus({ id, status }) {
  return request({
    url: "/hotelChannel/changeStatus",
    method: "post",
    data: {
      id,
      status
    }
  });
}