//pms对接接口
import request from "@/utils/request"; 

// 新增pms对接接口
export function addPmsInterface(data) {
  return request({
    url: "/hotelInterface/add",
    method: "post",
    data
  });
}

// 删除pms对接接口
export function deletePmsInterface(ids) {
  return request({
    url: "/hotelInterface/delete",
    method: "post",
    data: {
      ids: [].concat(ids)
    }
  });
}

// 修改pms对接接口信息
export function updatePmsInterface(data) {
  return request({
    url: "/hotelInterface/update",
    method: "post",
    data
  });
}

// 获取pms对接接口列表
export function getPmsInterfaceList(data) {
  return request({
    url: "/hotelInterface/getHotelInterfaceList",
    method: "post",
    data
  });
}

// 获取pms对接接口详情信息
export function getPmsInterfaceDetail(id) {
  return request({
    url: "/hotelInterface/getHotelInterface",
    method: "post",
    data: {
      id
    }
  });
}

// 修改pms对接接口状态
export function updatePmsInterfaceStatus(id, status) {
  return request({
    url: "/hotelInterface/changeStatus",
    method: "post",
    data: {
      id,
      status
    }
  });
}