//账号
import request from "@/utils/request"; 

// 新增账号
export function addHotelAccount(data) {
  return request({
    url: "/hotelAccount/add",
    method: "post",
    data
  });
}

// 删除账号
export function deleteHotelAccount(ids) {
  return request({
    url: "/hotelAccount/delete",
    method: "post",
    data: {
      ids: [].concat(ids)
    }
  });
}

// 修改账号信息
export function updateHotelAccount(data) {
  return request({
    url: "/hotelAccount/update",
    method: "post",
    data
  });
}

// 获取账号列表
export function getHotelAccountList(data) {
  return request({
    url: "/hotelAccount/getHotelAccountList",
    method: "post",
    data
  });
}

// 获取账号详情信息
export function getHotelAccountDetail(id) {
  return request({
    url: "/hotelAccount/getHotelAccount",
    method: "post",
    data: {
      id
    }
  });
}

// 修改账号状态
export function updateHotelAccountStatus({ id, status }) {
  return request({
    url: "/hotelAccount/changeStatus",
    method: "post",
    data: {
      id,
      status
    }
  });
}