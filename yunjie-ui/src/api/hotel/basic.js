//基础信息
import request from "@/utils/request";

//新增
export function addHotelInfo(data) {
  return request({
    url: "/hotelInfo/add",
    method: "post",
    data,
  });
}

//删除
export function deleteHotelInfo(ids) {
  return request({
    url: "/hotelInfo/delete",
    method: "post",
    data: {
      ids:[].concat(ids)
      //这里还有一个userName暂时不传输
    },
  });
}

//修改状态
export function changeHotelStatus(id, status) {
  return request({
    url: "/hotelInfo/changeStatus",
    method: "post",
    data: {
      id,
      status,
    },
  });
}

//更新
export function updateHotelInfo(data) {
  return request({
    url: "/hotelInfo/update",
    method: "post",
    data,
  });
}

//获取列表
export function getHotelInfoList(data) {
  return request({
    url: "/hotelInfo/getHotelInfoList",
    method: "post",
    data,
  });
}

//获取酒店详情信息
export function getHotelInfo(id){
  return request({
    url: "/hotelInfo/getHotelInfo",
    method: "post",
    data: {
      id,
    },
  });
}
