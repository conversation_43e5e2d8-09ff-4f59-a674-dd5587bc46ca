// 用户信息
import request from '@/utils/request';

// 获取酒店人员列表
export function getHotelUserList(query) {
  return request({
    url: '/hotelUser/getHotelUserList',
    method: 'post',
    data: query,
  });
}

// 获取酒店人员详情
export function getHotelUser(userId) {
  return request({
    url: '/hotelUser/getHotelUser',
    method: 'post',
    data: { userId },
  });
}

// 删除酒店用户
export function deleteHotelUser(ids) {
  return request({
    url: '/hotelUser/delete',
    method: 'post',
    data: { userIds: [].concat(ids) },
  });
}

// 更新用户状态
export function changeUserStatus(userId, status) {
  return request({
    url: '/hotelUser/changeStatus',
    method: 'post',
    data: { userId, status },
  });
}
