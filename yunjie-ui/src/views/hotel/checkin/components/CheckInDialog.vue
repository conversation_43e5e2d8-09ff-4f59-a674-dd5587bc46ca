<template>
  <el-dialog :visible.sync="dialogVisible" :title="title" width="500px" append-to-body>
    <el-form :model="checkinForm" :rules="rules" ref="checkinFormRef" label-width="100px">
      <el-form-item label="入住人姓名" prop="guestName">
        <el-input v-model="checkinForm.guestName" placeholder="请输入入住人姓名" />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="checkinForm.gender" placeholder="请选择性别" clearable>
          <el-option
            v-for="dict in dict.type.gender"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="证件类型" prop="idType">
        <el-select v-model="checkinForm.idType" placeholder="请选择证件类型" clearable>
          <el-option
            v-for="dict in dict.type.id_type"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="证件号码" prop="idNumber">
        <el-input v-model="checkinForm.idNumber" placeholder="请输入证件号码" />
      </el-form-item>

      <el-form-item label="入住时间" prop="checkinTime">
        <el-date-picker
          v-model="checkinForm.checkinTime"
          type="datetime"
          placeholder="请选择预计退房时间"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="退房时间" prop="checkoutTime">
        <el-date-picker
          v-model="checkinForm.checkoutTime"
          type="datetime"
          placeholder="请选择预计退房时间"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="入住天数" prop="stayDays">
        <el-input-number
          v-model="checkinForm.stayDays"
          placeholder="请输入入住天数"
        />
      </el-form-item>
      <el-form-item label="房间ID" prop="roomId">
        <el-input
          v-model="checkinForm.roomId"
          placeholder="请输入房间ID"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addCheckIn, updateCheckIn } from '@/api/hotel/checkin';

export default {
  name: 'CheckInDialog',
  dicts: ['checkin_type', 'gender', 'id_type'],
  props: {
    checkinData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      checkinForm: {},
      rules: {
        checkinName: [
          {
            required: true,
            message: '请输入入住信息名称',
            trigger: 'blur',
          },
        ],
        checkinContent: [
          {
            required: true,
            message: '请输入入住信息内容',
            trigger: 'blur',
          },
        ],
        checkinDesc: [
          {
            required: true,
            message: '请输入入住信息描述',
            trigger: 'blur',
          },
        ],
        checkinType: [
          {
            required: true,
            message: '请输入入住信息类型',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    title() {
      if (this.checkinForm.id != null) {
        return '修改入住信息信息';
      } else {
        return '新增入住信息';
      }
    },
  },
  methods: {
    open() {
      this.checkinForm = { ...this.checkinData };
      this.$refs.checkinFormRef?.resetFields();
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.checkinFormRef.validate((valid) => {
        if (valid) {
          if (this.checkinForm.id == null) {
            //调用新增接口
            addCheckIn(this.checkinForm)
              .then(() => {
                this.$message.success('新增成功');
                this.dialogVisible = false;
                this.$emit('refresh');
              })
              .catch(() => {});
          } else {
            //调用修改接口
            updateCheckIn(this.checkinForm)
              .then(() => {
                this.$message.success('修改成功');
                this.dialogVisible = false;
                this.$emit('refresh');
              })
              .catch(() => {});
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped></style>
