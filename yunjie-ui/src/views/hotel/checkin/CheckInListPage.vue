<template>
  <div>
    <el-form :model="queryParams" size="small" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.checkin_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="checkinList">
      <el-table-column label="入住人姓名" align="center" prop="guestName" width="120" />
      <el-table-column label="性别" align="center" prop="gender">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.gender" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="phoneNumber" width="120" />
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.checkin_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="入住时间" align="center" prop="checkinTime" width="100" />
      <el-table-column label="离店时间" align="center" prop="checkoutTime" width="100" />
      <el-table-column
        label="预计退房时间"
        align="center"
        prop="expectedCheckoutTime"
        width="100"
      />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">
            修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />

    <CheckInDialog ref="checkinDialogRef" :checkinData="checkinData" />
  </div>
</template>

<script>
import { getCheckInList, getCheckInDetail } from '@/api/hotel/checkin';
import CheckInDialog from './components/CheckInDialog.vue';

export default {
  name: 'CheckInListPage',
  dicts: ['checkin_status','gender'],
  props: {
    hotel: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    CheckInDialog,
  },
  data() {
    return {
      loading: false,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkinName: undefined,
        checkinNo: undefined,
        checkinModel: undefined,
        checkinType: undefined,
        checkinChannel: undefined,
        status: '0',
      },
      total: 0,
      checkinList: [],
      checkinData: {},
    };
  },
  methods: {
    handleCheckInList() {
      this.loading = true;
      this.queryParams.hotelCode = this.hotel.hotelCode;
      this.queryParams.hotelName = this.hotel.hotelName;
      getCheckInList(this.queryParams)
        .then((res) => {
          this.checkinList = res?.rows;
          this.total = res?.total;
        })
        .catch(() => {
          this.checkinList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.handleCheckInList();
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleUpdate(row) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '信息加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.8)',
      });
      getCheckInDetail(row.id)
        .then((res) => {
          this.checkinData = res?.data;
          this.$nextTick(() => {
            this.$refs.checkinDialogRef.open();
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingInstance.close();
        });
    },
  },
};
</script>

<style scoped></style>
