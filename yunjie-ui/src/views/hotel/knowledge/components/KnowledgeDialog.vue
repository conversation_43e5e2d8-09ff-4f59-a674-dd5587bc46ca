<template>
  <el-dialog :visible.sync="dialogVisible" :title="title" width="500px" append-to-body>
    <el-form :model="knowledgeForm" :rules="rules" ref="knowledgeFormRef" label-width="100px">
      <el-form-item label="知识库名称" prop="knowledgeName">
        <el-input v-model="knowledgeForm.knowledgeName" placeholder="请输入知识库名称" />
      </el-form-item>
      <el-form-item label="知识库内容" prop="knowledgeContent">
        <el-input
          v-model="knowledgeForm.knowledgeContent"
          placeholder="请输入知识库内容"
          type="textarea"
        />
      </el-form-item>
      <el-form-item label="知识库类型" prop="knowledgeType">
        <el-select v-model="knowledgeForm.knowledgeType" placeholder="请选择知识库类型" clearable>
          <el-option
            v-for="dict in dict.type.knowledge_type"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="知识库描述" prop="knowledgeDesc">
        <el-input
          v-model="knowledgeForm.knowledgeDesc"
          placeholder="请输入知识库描述"
          type="textarea"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addKnowledge, updateKnowledge } from '@/api/hotel/knowledge';

export default {
  name: 'KnowledgeDialog',
  dicts: ['knowledge_type'],
  props: {
    knowledgeData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      knowledgeForm: {},
      rules: {
        knowledgeName: [
          {
            required: true,
            message: '请输入知识库名称',
            trigger: 'blur',
          },
        ],
        knowledgeContent: [
          {
            required: true,
            message: '请输入知识库内容',
            trigger: 'blur',
          },
        ],
        knowledgeDesc: [
          {
            required: true,
            message: '请输入知识库描述',
            trigger: 'blur',
          },
        ],
        knowledgeType: [
          {
            required: true,
            message: '请输入知识库类型',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    title() {
      if (this.knowledgeForm.id != null) {
        return '修改知识库信息';
      } else {
        return '新增知识库';
      }
    },
  },
  methods: {
    open() {
      this.knowledgeForm = { ...this.knowledgeData };
      this.$refs.knowledgeFormRef?.resetFields();
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.knowledgeFormRef.validate((valid) => {
        if (valid) {
          if (this.knowledgeForm.id == null) {
            //调用新增接口
            addKnowledge(this.knowledgeForm)
              .then(() => {
                this.$message.success('新增成功');
                this.dialogVisible = false;
                this.$emit('refresh');
              })
              .catch(() => {});
          } else {
            //调用修改接口
            updateKnowledge(this.knowledgeForm)
              .then(() => {
                this.$message.success('修改成功');
                this.dialogVisible = false;
                this.$emit('refresh');
              })
              .catch(() => {});
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped></style>
