<template>
  <div>
    <el-form :model="queryParams" size="small" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="知识库名称" prop="knowledgeName">
        <el-input
          v-model="queryParams.knowledgeName"
          placeholder="请输入知识库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="知识库状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择知识库状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addKnowledge">
          新增知识库
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          :disabled="multiple"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="knowledgeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="知识库名称" align="center" prop="knowledgeName" />
      <el-table-column label="内容" align="center" prop="knowledgeContent" />
      <el-table-column label="描述" align="center" prop="knowledgeDesc" />
      <el-table-column label="类型" align="center" prop="knowledgeType" />
      <el-table-column label="状态" align="center" width="200">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-text="启用"
            inactive-text="停用"
            :active-value="0"
            :inactive-value="1"
            size="mini"
            @change="(val) => handleStatusChange(scope.row.id, val)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">
            修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />
    <KnowledgeDialog
      ref="knowledgeDialogRef"
      :knowledgeData="knowledgeData"
      @refresh="handleQuery"
    />
  </div>
</template>

<script>
import {
  deleteKnowledge,
  getKnowledgeList,
  getKnowledgeDetail,
  updateKnowledgeStatus,
} from '@/api/hotel/knowledge';
import KnowledgeDialog from './components/KnowledgeDialog';

export default {
  name: 'KnowledgeListPage',
  dicts: ['sys_status'],
  components: {
    KnowledgeDialog,
  },
  props: {
    hotel: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        knowledgeName: undefined,
        status: '-1',
      },
      ids: [],
      single: true,
      multiple: true,
      total: 0,
      knowledgeList: [],
      knowledgeData: {},
    };
  },
  methods: {
    handleKnowledgeList() {
      this.loading = true;
      this.queryParams.hotelCode = this.hotel.hotelCode;
      this.queryParams.hotelName = this.hotel.hotelName;

      getKnowledgeList(this.queryParams)
        .then((res) => {
          this.knowledgeList = res?.rows;
          this.total = res?.total;
        })
        .catch(() => {
          this.knowledgeList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.handleKnowledgeList();
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = selection.length === 0;
    },
    addKnowledge() {
      this.knowledgeData = { hotelId: this.hotel.id };
      this.$nextTick(() => {
        this.$refs.knowledgeDialogRef.open();
      });
    },
    handleUpdate(row) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '信息加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.8)',
      });
      getKnowledgeDetail(row.id == null ? this.ids[0] : row.id)
        .then((res) => {
          this.knowledgeData = res?.data;
          this.$nextTick(() => {
            this.$refs.knowledgeDialogRef.open();
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingInstance.close();
        });
    },
    handleDelete({ knowledgeName, id }) {
      const title = !knowledgeName
        ? '确认要删除选中的知识库吗?'
        : `你确认要删除${knowledgeName}吗？`;
      this.$confirm(title, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        deleteKnowledge(id || this.ids)
          .then((res) => {
            this.handleKnowledgeList();
            this.$message.success('删除成功');
          })
          .catch(() => {});
      });
    },
    handleStatusChange(id, status) {
      updateKnowledgeStatus(id, status)
        .then(() => {
          this.$message.success('状态切换成功！');
          this.handleKnowledgeList();
        })
        .catch(() => {});
    },
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
      }
    },
  },
};
</script>

<style scoped></style>
