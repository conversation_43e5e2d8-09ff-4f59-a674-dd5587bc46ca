<template>
  <el-dialog :visible.sync="dialogVisible" title="PMS 信息" append-to-body>
    <el-form :model="pmsForm" :rules="rules" ref="pmsFormRef" label-width="100px">
      <el-form-item label="域名地址" prop="domainUrl">
        <el-input v-model="pmsForm.domainUrl" placeholder="请输入域名地址" />
      </el-form-item>
      <el-form-item label="接口地址" prop="interfaceUrl">
        <el-input v-model="pmsForm.interfaceUrl" placeholder="请输入接口地址" />
      </el-form-item>
      <el-form-item label="接口描述" prop="interfaceDesc">
        <el-input v-model="pmsForm.interfaceDesc" placeholder="请输入接口描述" type="textarea" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addPmsInterface, updatePmsInterface } from '@/api/hotel/pms';

export default {
  name: 'PmsDialog',
  props: {
    pmsData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      pmsForm: {},
      rules: {
        domainUrl: [{ required: true, message: '请输入域名地址', trigger: 'blur' }],
        interfaceDesc: [
          {
            required: true,
            message: '请输入接口描述',
            trigger: 'blur',
          },
        ],
        interfaceUrl: [
          {
            required: true,
            message: '请输入接口地址',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    title() {
      if (this.pmsForm.id != null) {
        return '修改PMS地址信息';
      } else {
        return '新增PMS地址信息';
      }
    },
  },
  methods: {
    open() {
      this.pmsForm = { ...this.pmsData };
      this.$refs.pmsFormRef?.resetFields();
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.pmsFormRef.validate((valid) => {
        if (valid) {
          if (this.pmsForm.id) {
            updatePmsInterface(this.pmsForm).then(() => {
              this.$message.success('更新成功');
              this.dialogVisible = false;
              this.$emit('refresh');
            });
          } else {
            addPmsInterface(this.pmsForm).then(() => {
              this.$message.success('新增成功');
              this.dialogVisible = false;
              this.$emit('refresh');
            });
          }
        }
      });
    },
  },
};
</script>

<style scoped></style>
