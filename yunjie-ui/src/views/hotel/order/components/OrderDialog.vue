<template>
  <el-dialog title="订单详情" :visible.sync="dialogVisible" width="80%" append-to-body>
    <el-descriptions :column="2" border label-class-name="fixed-width-label">
      <el-descriptions-item label="预定人">
        {{ orderData.orderName }}
      </el-descriptions-item>
      <el-descriptions-item label="订单号">
        {{ orderData.orderNo }}
      </el-descriptions-item>
      <el-descriptions-item label="酒店ID" :span="2">
        {{ orderData.hotelId }}
      </el-descriptions-item>
      <el-descriptions-item label="酒店名称" :span="2">
        {{ orderData.hotelName }}
      </el-descriptions-item>
      <el-descriptions-item label="预抵时间">
        {{ orderData.arriveTime }}
      </el-descriptions-item>
      <el-descriptions-item label="预离时间">
        {{ orderData.leaveTime }}
      </el-descriptions-item>
      <el-descriptions-item label="保留时间">
        {{ orderData.keepTime }}
      </el-descriptions-item>
      <el-descriptions-item label="最后取消时间">
        {{ orderData.lastCancelTime }}
      </el-descriptions-item>
      <el-descriptions-item label="入住方式">
        <dict-tag :options="dict.type.checkin_model" :value="orderData.checkinModel" />
      </el-descriptions-item>
      <el-descriptions-item label="入住类型">
        <dict-tag :options="dict.type.checkin_model" :value="orderData.checkinType" />
      </el-descriptions-item>
      <el-descriptions-item label="添加人">张三</el-descriptions-item>
      <el-descriptions-item label="添加时间">
        {{ orderData.createdAt }}
      </el-descriptions-item>
      <el-descriptions-item label="客源类型">
        {{ orderData.customType }}
      </el-descriptions-item>
      <el-descriptions-item label="入住天数">
        {{ orderData.days }}
      </el-descriptions-item>
      <el-descriptions-item label="住客来源">
        {{ orderData.guestSource == '0' ? '国内' : '境外' }}
      </el-descriptions-item>
      <el-descriptions-item label="计费规则">
        {{ orderData.hoursRule }}
      </el-descriptions-item>
      <el-descriptions-item label="开票类型">
        {{ orderData.invoiceType == 0 ? '无' : '无需开票' }}
      </el-descriptions-item>
      <el-descriptions-item label="是否虚拟订单">
        {{ orderData.isVirtual == 1 ? '是' : '不是' }}
      </el-descriptions-item>
      <el-descriptions-item label="订单备注">
        {{ orderData.meno }}
      </el-descriptions-item>
      <el-descriptions-item label="订单来源">
        {{ orderData.orderChannel }}
      </el-descriptions-item>
      <el-descriptions-item label="是否预定">
        {{ orderData.orderFlag == 1 ? '是' : '不是' }}
      </el-descriptions-item>
      <el-descriptions-item label="订单状态">
        <dict-tag :options="dict.type.order_status" :value="orderData.orderStatus" />
      </el-descriptions-item>
      <el-descriptions-item label="手机号">
        {{ orderData.orderTel }}
      </el-descriptions-item>
      <el-descriptions-item label="OTA渠道">
        <dict-tag :options="dict.type.ota_channel" :value="orderData.otaChannel" />
      </el-descriptions-item>
      <el-descriptions-item label="外部订单号">
        {{ orderData.outNo }}
      </el-descriptions-item>
      <el-descriptions-item label="OTA付款类型">
        <dict-tag :options="dict.type.pay_type" :value="orderData.payTypeCode" />
      </el-descriptions-item>
      <el-descriptions-item label="订单总价">
        {{ orderData.price }}
      </el-descriptions-item>
    </el-descriptions>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'OrderDialog',
  dicts: [
    'checkin_model',
    'checkin_type',
    'channel_type',
    'sys_yes_no',
    'ota_channel',
    'order_status',
    'pay_type',
  ],
  props: {
    orderData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
  methods: {
    open() {
      this.dialogVisible = true;
    },
  },
};
</script>

<style>
.el-dialog {
  overflow: auto;
}

::v-deep .fixed-width-descriptions .el-descriptions__cell {
  width: 200px !important;
  box-sizing: border-box;
}

.fixed-width-label {
  width: 140px !important;
}
</style>
