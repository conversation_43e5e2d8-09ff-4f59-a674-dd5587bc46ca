<template>
  <div class="container">
    <el-carousel
      ref="carousel"
      class="el-carousel"
      arrow="never"
      height="100%"
      :autoplay="false"
      :loop="false"
      indicator-position="none"
    >
      <el-carousel-item>
        <HotelSelector @selected="handleHotelSelect" />
      </el-carousel-item>
      <el-carousel-item>
        <div class="app-container scroll-y">
          <el-page-header @back="goBack" content="订单列表"></el-page-header>
          <el-alert
            class="margin-top"
            :title="alertTitle"
            type="success"
            :closable="false"
          ></el-alert>
          <br />
          <OrderListPage ref="orderListPageRef" :hotel="selectedHotel" />
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
import HotelSelector from '@/components/HotelSelector/index.vue';
import OrderListPage from './OrderListPage.vue';

export default {
  components: {
    HotelSelector,
    OrderListPage,
  },
  computed: {
    alertTitle() {
      return this.selectedHotel ? `当前酒店：${this.selectedHotel.hotelName}` : '当前酒店：未选择';
    },
  },
  data() {
    return {
      selectedHotel: null,
    };
  },
  methods: {
    handleHotelSelect(hotel) {
      this.selectedHotel = hotel;
      this.$nextTick(() => {
        // 切换到第二个页面
        this.$refs.carousel.next();
        // 页面信息刷新
        this.$refs.orderListPageRef.resetQuery();
      });
    },
    goBack() {
      this.$refs.carousel.prev();
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  display: flow-root;
  height: 100%;
  position: absolute;
  width: 100%;

  .el-carousel {
    height: 100%;
  }
}
</style>
