<template>
  <div>
    <el-form :model="queryParams" size="small" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="预订人" prop="orderName">
        <el-input
          v-model="queryParams.orderName"
          placeholder="请输入预订人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入住方式" prop="checkinModel">
        <el-select v-model="queryParams.checkinModel" placeholder="请选择入住方式" clearable>
          <el-option
            v-for="dict in dict.type.checkin_model"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="入住类型" prop="checkinType">
        <el-select v-model="queryParams.checkinType" placeholder="请选择入住类型" clearable>
          <el-option
            v-for="dict in dict.type.checkin_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单来源" prop="orderChannel">
        <el-select v-model="queryParams.orderChannel" placeholder="请选择订单来源" clearable>
          <el-option
            v-for="dict in dict.type.channel_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList">
      <el-table-column label="订单名称" align="center" prop="orderName" />
      <el-table-column label="订单电话" align="center" prop="orderTel" />
      <el-table-column label="订单渠道" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.channel_type" :value="scope.row.orderChannel" />
        </template>
      </el-table-column>
      <el-table-column label="入住类型" align="center">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.checkin_type" :value="scope.row.checkinType" />
        </template>
      </el-table-column>
      <el-table-column label="内容" align="center" prop="orderContent" />
      <el-table-column label="描述" align="center" prop="orderDesc" />
      <el-table-column label="类型" align="center" prop="orderType" />
      <el-table-column label="状态" align="center" width="200">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-text="启用"
            inactive-text="停用"
            :active-value="0"
            :inactive-value="1"
            size="mini"
            @change="(val) => handleStatusChange(scope.row.id, val)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="handleShowDetail(scope.row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />
    <OrderDialog ref="orderDialogRef" :orderData="orderData" @refresh="handleQuery" />
  </div>
</template>

<script>
import { getOrderList, getOrderDetail } from '@/api/hotel/order';
import OrderDialog from './components/OrderDialog';

export default {
  name: 'OrderListPage',
  dicts: ['checkin_model', 'checkin_type', 'channel_type'],
  components: {
    OrderDialog,
  },
  props: {
    hotel: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderName: undefined,
        orderNo: undefined,
        checkinModel: undefined,
        checkinType: undefined,
        orderChannel: undefined,
        status: '-1',
      },
      total: 0,
      orderList: [],
      orderData: {},
    };
  },
  methods: {
    handleOrderList() {
      this.loading = true;
      this.queryParams.hotelCode = this.hotel.hotelCode;
      this.queryParams.hotelName = this.hotel.hotelName;
      getOrderList(this.queryParams)
        .then((res) => {
          this.orderList = res?.rows;
          this.total = res?.total;
        })
        .catch(() => {
          this.orderList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.handleOrderList();
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleShowDetail(row) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '信息加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.8)',
      });
      getOrderDetail(row.id)
        .then((res) => {
          this.orderData = res?.data;
          this.$nextTick(() => {
            this.$refs.orderDialogRef.open();
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingInstance.close();
        });
    },
  },
};
</script>

<style scoped></style>
