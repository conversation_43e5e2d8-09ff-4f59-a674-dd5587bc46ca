<template>
  <el-dialog :visible.sync="dialogVisible" :title="title" width="500px" append-to-body>
    <el-form :model="adForm" :rules="rules" ref="adForm" label-width="100px">
      <el-form-item label="广告名称" prop="adName">
        <el-input v-model="adForm.adName" placeholder="请输入广告名称" />
      </el-form-item>
      <el-form-item label="广告地址" prop="adUrl">
        <el-input v-model="adForm.adUrl" placeholder="请输入广告地址" />
      </el-form-item>
      <el-form-item label="广告描述" prop="adDesc">
        <el-input v-model="adForm.adDesc" placeholder="请输入广告描述" type="textarea" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addAd, updateAd } from '@/api/hotel/ad';

export default {
  name: 'AdDialog',
  props: {
    adData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      adForm: {
        adDesc: '',
        adName: '',
        adUrl: '',
        hotelId: this.hotelId,
      },
      rules: {
        adName: [
          {
            required: true,
            message: '请输入广告名称',
            trigger: 'blur',
          },
        ],
        adUrl: [
          {
            required: true,
            message: '请输入广告地址',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    title() {
      if (this.adForm.id != null) {
        return '修改广告信息';
      } else {
        return '新增广告';
      }
    },
  },
  methods: {
    open() {
      this.adForm = { ...this.adData };
      this.$refs.adForm?.resetFields();
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.adForm.validate((valid) => {
        if (valid) {
          if (this.adForm.id == null) {
            //调用新增接口
            addAd(this.adForm)
              .then(() => {
                this.$message.success('新增成功');
                this.dialogVisible = false;
                this.$emit('refresh');
              })
              .catch(() => {});
          } else {
            //调用修改接口
            updateAd(this.adForm)
              .then(() => {
                this.$message.success('修改成功');
                this.dialogVisible = false;
                this.$emit('refresh');
              })
              .catch(() => {});
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style scoped></style>
