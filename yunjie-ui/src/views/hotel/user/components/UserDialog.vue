<template>
  <el-dialog title="用户信息详情" :visible.sync="dialogVisible">
    <el-form label-width="100px">
      <el-form-item label="昵称">
        <el-input v-model="userData.nickName" disabled />
      </el-form-item>
      <el-form-item label="OpenID">
        <el-input v-model="userData.openid" disabled />
      </el-form-item>
      <el-form-item label="头像">
        <el-image :src="userData.mobile" class="avatar-image" fit="cover">
          <div slot="error">
            <div class="image-slot">无</div>
          </div>
        </el-image>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="userData.mobile" disabled />
      </el-form-item>
      <el-form-item label="绑定状态">
        <el-select v-model="userData.bindFlag" disabled>
          <el-option
            v-for="dict in dict.type.user_bind_flag"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员状态">
        <el-select v-model="userData.status" disabled>
          <el-option
            v-for="dict in dict.type.sys_status"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'UserDialog',
  dicts: ['sys_status', 'user_bind_flag'],
  props: {
    userData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
  computed: {
    // 计算属性
  },
  methods: {
    open() {
      this.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.avatar-image {
  width: 3rem;
  height: 3rem;
  border-radius: 10%;

  .image-slot {
    background-color: #e9e7e7;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 3rem;
    height: 3rem;
    font-size: 12px;
  }
}
</style>
