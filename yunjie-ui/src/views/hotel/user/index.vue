<template>
  <div class="app-container">
    <el-form :model="queryParams" size="small" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="用户手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="OpenID" prop="openid">
        <el-input
          v-model="queryParams.openid"
          placeholder="请输入OpenID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择用户状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          :disabled="multiple"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getUserList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="昵称" align="center" prop="nickName" />
      <el-table-column label="OpenID" align="center" prop="openid" width="120" />
      <el-table-column label="头像" align="center">
        <template slot-scope="scope">
          <el-image :src="scope.row.avatarUrl" class="avatar-image" fit="cover">
            <div slot="error">
              <div class="image-slot">无</div>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="mobile" width="120" />
      <el-table-column label="绑定状态" align="center" width="200">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.user_bind_flag" :value="scope.row.bindFlag" />
        </template>
      </el-table-column>
      <el-table-column label="人员状态" align="center" width="200">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-text="有效"
            inactive-text="失效"
            :active-value="0"
            :inactive-value="1"
            size="mini"
            @change="(val) => handleStatusChange(scope.row.userId, val)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="handleCheckUser(scope.row)"
          >
            查看
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />
    <!-- 人员详情 -->
    <UserDialog ref="userDialogRef" :userData="userData" />
  </div>
</template>

<script>
import UserDialog from './components/UserDialog.vue';

import {
  getHotelUserList,
  changeUserStatus,
  deleteHotelUser,
  getHotelUser,
} from '@/api/hotel/user';

export default {
  name: 'userListPage',
  dicts: ['sys_status', 'user_bind_flag'],
  components: {
    UserDialog,
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mobile: '',
        openid: '',
        status: '-1',
      },
      ids: [],
      names: [],
      single: true,
      multiple: true,
      total: 0,
      userList: [],
      userData: {},
    };
  },
  mounted() {
    this.getUserList();
  },
  methods: {
    getUserList() {
      this.loading = true;
      getHotelUserList(this.queryParams)
        .then((res) => {
          this.userList = res?.rows;
          this.total = res?.total;
        })
        .catch(() => {
          this.userList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.getUserList();
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      const { ids, names } = selection.reduce(
        (acc, item) => ({
          ids: [...acc.ids, item.userId],
          names: [...acc.names, item.nickName],
        }),
        { ids: [], names: [] }
      );
      this.ids = ids;
      this.names = names;
      this.single = selection.length !== 1;
      this.multiple = selection.length === 0;
    },
    handleCheckUser(row) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '信息加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.8)',
      });
      getHotelUser(row.userId)
        .then((res) => {
          this.userData = res?.data;
          this.$refs.userDialogRef.open();
        })
        .catch(() => {})
        .finally(() => {
          loadingInstance.close();
        });
    },
    handleDelete({ nickName, userId }) {
      const title = `你确认要删除${nickName || this.names}吗？`;
      this.$confirm(title, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        deleteHotelUser(userId || this.ids)
          .then((res) => {
            this.getUserList();
            this.$message.success('删除成功');
          })
          .catch(() => {});
      });
    },
    handleStatusChange(id, status) {
      changeUserStatus(id, status)
        .then(() => {
          this.$message.success('状态切换成功！');
          this.getUserList();
        })
        .catch(() => {});
    },
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.avatar-image {
  width: 3rem;
  height: 3rem;
  border-radius: 10%;

  .image-slot {
    background-color: #e9e7e7;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 3rem;
    height: 3rem;
    font-size: 12px;
  }
}
</style>
