<template>
  <el-dialog title="预定信息详情" :visible.sync="dialogVisible" width="80%" append-to-body>
    <el-descriptions :column="2" border label-class-name="fixed-width-label">
      <el-descriptions-item label="酒店名称" :span="2">
        {{ bookingData.hotelName }}
      </el-descriptions-item>
      <el-descriptions-item label="状态" :span="2">
        <dict-tag :options="dict.type.booking_status" :value="bookingData.status" />
      </el-descriptions-item>
      <el-descriptions-item label="预定人">
        {{ bookingData.contactName }}
      </el-descriptions-item>
      <el-descriptions-item label="预订人电话">
        {{ bookingData.contactPhone }}
      </el-descriptions-item>
      <el-descriptions-item label="预抵时间" :span="2">
        {{ bookingData.arrivalTime }}
      </el-descriptions-item>
      <el-descriptions-item label="入住日期">
        {{ bookingData.checkinDate }}
      </el-descriptions-item>
      <el-descriptions-item label="离店日期">
        {{ bookingData.checkoutDate }}
      </el-descriptions-item>
      <el-descriptions-item label="房间数量">
        {{ bookingData.roomCount }}
      </el-descriptions-item>
      <el-descriptions-item label="房间类型">
        {{ bookingData.roomType }}
      </el-descriptions-item>
      <el-descriptions-item label="预定备注" :span="2">
        {{ bookingData.remark }}
      </el-descriptions-item>
    </el-descriptions>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'OrderDialog',
  dicts: ['booking_status'],
  props: {
    bookingData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
  methods: {
    open() {
      this.dialogVisible = true;
    },
  },
};
</script>

<style>
.el-dialog {
  overflow: auto;
}

::v-deep .fixed-width-descriptions .el-descriptions__cell {
  width: 200px !important;
  box-sizing: border-box;
}

.fixed-width-label {
  width: 140px !important;
}
</style>
