<template>
  <div>
    <el-form :model="queryParams" size="small" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.booking_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bookingList">
      <el-table-column label="预订人" align="center" prop="contactName" />
      <el-table-column label="预订人电话" align="center" prop="contactPhone" width="200" />
      <el-table-column label="房间数量" align="center" prop="roomCount" />
      <el-table-column label="房型" align="center" prop="roomTypeId" />
      <el-table-column label="创建人" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="200" />
      <el-table-column label="客户备注" align="center" prop="remark" />
      <el-table-column label="状态" align="center" width="200">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.booking_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="handleShowDetail(scope.row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />

    <BookingDialog ref="bookingDialogRef" :bookingData="bookingData" />
  </div>
</template>

<script>
import { getBookingList, getBookingDetail } from '@/api/hotel/booking';
import BookingDialog from './components/BookingDialog.vue';

export default {
  name: 'BookingListPage',
  dicts: ['booking_status'],
  props: {
    hotel: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    BookingDialog,
  },
  data() {
    return {
      loading: false,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bookingName: undefined,
        bookingNo: undefined,
        checkinModel: undefined,
        checkinType: undefined,
        bookingChannel: undefined,
        status: '0',
      },
      total: 0,
      bookingList: [],
      bookingData: {},
    };
  },
  methods: {
    handleBookingList() {
      this.loading = true;
      this.queryParams.hotelCode = this.hotel.hotelCode;
      this.queryParams.hotelName = this.hotel.hotelName;
      getBookingList(this.queryParams)
        .then((res) => {
          this.bookingList = res?.rows;
          this.total = res?.total;
        })
        .catch(() => {
          this.bookingList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.handleBookingList();
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleShowDetail(row) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '信息加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.8)',
      });
      getBookingDetail(row.id)
        .then((res) => {
          this.bookingData = res?.data;
          this.$nextTick(() => {
            this.$refs.bookingDialogRef.open();
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingInstance.close();
        });
    },
  },
};
</script>

<style scoped></style>
