<template>
  <div class="app-container scroll-y">
    <el-page-header @back="goBack" content="酒店账号详情"></el-page-header>
    <el-alert class="margin-top" :title="title" type="success" :closable="false" />
    <br />
    <div>
      <el-form :model="queryParams" size="small" ref="queryForm" :inline="true" v-show="showSearch">
        <el-form-item label="员工姓名" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入账号名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input
            v-model="queryParams.mobile"
            placeholder="请输入手机号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="账号状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择账号状态" clearable>
            <el-option
              v-for="dict in dict.type.sys_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAddHotelAccount"
          >
            新增账号
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
          >
            修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            :disabled="multiple"
            size="mini"
            @click="handleDelete"
          >
            删除
          </el-button>
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getHotelAccountList"
        ></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="accountList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="姓名" align="center" prop="name" />
        <el-table-column label="手机号" align="center" prop="mobile" />
        <el-table-column label="状态" align="center" width="200">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              :active-value="0"
              :inactive-value="1"
              size="mini"
              @change="(val) => handleStatusChange(scope.row.id, val)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">
              修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
      />
      <AccountDialog ref="accountDialogRef" :accountData="accountData" @refresh="handleQuery" />
    </div>
  </div>
</template>

<script>
import {
  deleteHotelAccount,
  getHotelAccountList,
  getHotelAccountDetail,
  updateHotelAccountStatus,
} from '@/api/hotel/account';
import AccountDialog from './components/AccountDialog';

export default {
  name: 'HotelAccount',
  dicts: ['sys_status'],
  props: {
    hotel: {
      type: Object,
      default: {},
    },
  },
  components: {
    AccountDialog,
  },
  computed: {
    title() {
      return '当前酒店：' + this.hotel?.hotelName;
    },
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mobile:undefined,
        name:undefined,
        status: '-1',
      },
      ids: [],
      single: true,
      multiple: true,
      total: 0,
      accountList: [],
      accountData: {},
    };
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },
    getHotelAccountList() {
      this.loading = true;
      this.queryParams.hotelCode = this.hotel.hotelCode;
      this.queryParams.hotelName = this.hotel.hotelName;

      getHotelAccountList(this.queryParams)
        .then((res) => {
          this.accountList = res?.rows;
          this.total = res?.total;
        })
        .catch(() => {
          this.accountList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.getHotelAccountList();
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = selection.length === 0;
    },
    handleAddHotelAccount() {
      this.accountData = { hotelId: this.hotel.id };
      this.$nextTick(() => {
        this.$refs.accountDialogRef.open();
      });
    },
    handleUpdate(row) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '信息加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.8)',
      });
      // 修改接口调用
      getHotelAccountDetail(row.id == null ? this.ids[0] : row.id)
        .then((res) => {
          this.accountData = res?.data;
          this.$nextTick(() => {
            this.$refs.accountDialogRef.open();
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingInstance.close();
        });
    },
    handleDelete({ accountId, id }) {
      const title = !accountId ? '确认要删除选中的账号吗?' : `你确认要删除${accountId}吗？`;
      this.$confirm(title, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        deleteHotelAccount(id || this.ids)
          .then((res) => {
            this.getHotelAccountList();
            this.$message.success('删除成功');
          })
          .catch(() => {});
      }).catch(()=>{});
    },
    handleStatusChange(id, status) {
      const originalStatus = status === 0 ? 1 : 0;
      const row = this.accountList.find((item) => item.id === id);
      if (row) {
        updateHotelAccountStatus({ id, status })
          .then(() => {
            this.$message.success('状态切换成功');
          })
          .catch(() => {
            row.status = originalStatus;
            this.$message.error('状态切换失败');
          });
      }
    },
    //回退
    goBack() {
      this.$emit('goBack');
    },
  },
};
</script>

<style scoped></style>
