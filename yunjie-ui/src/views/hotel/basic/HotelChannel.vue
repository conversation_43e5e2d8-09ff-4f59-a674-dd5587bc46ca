<template>
  <div class="app-container scroll-y">
    <el-page-header @back="goBack" content="酒店渠道详情"></el-page-header>
    <el-alert class="margin-top" :title="title" type="success" :closable="false" />
    <br />
    <div>
      <el-form :model="queryParams" size="small" ref="queryForm" :inline="true" v-show="showSearch">
        <el-form-item label="渠道名称" prop="channelName">
          <el-input
            v-model="queryParams.channelName"
            placeholder="请输入渠道名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="渠道状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择渠道状态" clearable>
            <el-option
              v-for="dict in dict.type.sys_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addChannel">
            新增渠道
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
          >
            修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            :disabled="multiple"
            size="mini"
            @click="handleDelete"
          >
            删除
          </el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getChannelList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="channelList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="账号" align="center" prop="accountId" />
        <el-table-column label="类型" align="center" prop="channelType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.channel_type" :value="scope.row.channelType" />
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center" width="200">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-text="启用"
              inactive-text="停用"
              :active-value="0"
              :inactive-value="1"
              size="mini"
              @change="(val) => handleStatusChange(scope.row.id, val)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">
              修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
      />

      <!-- 更新渠道弹窗 -->
      <ChannelDialog ref="channelDialogRef" :channelData="channelData" @refresh="handleQuery" />
    </div>
  </div>
</template>

<script>
import {
  addChannel,
  deleteChannel,
  getChannelList,
  getChannelDetail,
  updateChannelStatus,
} from '@/api/hotel/channel';
import ChannelDialog from './components/ChannelDialog';

export default {
  name: 'HotelChannel',
  dicts: ['sys_status', 'channel_type'],
  props: {
    hotel: {
      type: Object,
      default: {},
    },
  },
  components: {
    ChannelDialog,
  },
  computed: {
    title() {
      return '当前酒店：' + this.hotel?.hotelName;
    },
  },
  data() {
    return {
      loading: true,
      showSearch: true,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        channelName: undefined,
        status: '-1',
      },
      ids: [],
      single: true,
      multiple: true,
      total: 0,
      channelList: [],
      channelData: {},
    };
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },
    getChannelList() {
      this.loading = true;
      this.queryParams.hotelCode = this.hotel.hotelCode;
      this.queryParams.hotelName = this.hotel.hotelName;

      getChannelList(this.queryParams)
        .then((res) => {
          this.channelList = res?.rows;
          this.total = res?.total;
        })
        .catch(() => {
          this.channelList = [];
          this.total = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleQuery() {
      this.getChannelList();
    },
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = selection.length === 0;
    },
    addChannel() {
      this.channelData = { hotelId: this.hotel.id };
      this.$nextTick(() => {
        this.$refs.channelDialogRef.open();
      });
    },
    handleUpdate(row) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '信息加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.8)',
      });
      getChannelDetail(row.id == null ? this.ids[0] : row.id)
        .then((res) => {
          this.channelData = res?.data;
          this.$nextTick(() => {
            this.$refs.channelDialogRef.open();
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingInstance.close();
        });
    },
    handleDelete({ accountId, id }) {
      const title = !accountId ? '确认要删除选中的渠道吗?' : `你确认要删除${accountId}吗？`;
      this.$confirm(title, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        deleteChannel(id || this.ids)
          .then((res) => {
            this.getChannelList();
            this.$message.success('删除成功');
          })
          .catch(() => {});
      }).catch(()=>{});;
    },
    handleStatusChange(id, status) {
      const originalStatus = status === 0 ? 1 : 0;
      const row = this.channelList.find((item) => item.id === id);
      if (row) {
        updateChannelStatus({ id, status })
          .then(() => {
            this.$message.success('状态切换成功');
          })
          .catch(() => {
            row.status = originalStatus;
            this.$message.error('状态切换失败');
          });
      }
    },
    //回退
    goBack() {
      this.$emit('goBack');
    },
  },
};
</script>

<style scoped></style>
