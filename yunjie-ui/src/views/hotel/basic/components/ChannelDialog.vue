<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="50%"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="small">
      <el-form-item label="渠道账号" prop="accountId">
        <el-input v-model="form.accountId" placeholder="请输入渠道账号" autocomplete="off" />
      </el-form-item>
      <el-form-item label="渠道密码" prop="password">
        <el-input
          v-model="form.password"
          show-password
          placeholder="请输入渠道密码"
          autocomplete="new-password"
        />
      </el-form-item>
      <el-form-item label="渠道种类" prop="channelType">
        <el-select
          v-model="form.channelType"
          placeholder="请选择渠道种类"
          :disabled="form.id != null"
        >
          <el-option
            v-for="dict in dict.type.channel_type"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addChannel, updateChannel } from '@/api/hotel/channel';

export default {
  name: 'ChannelDialog',
  dicts: ['channel_type'],
  props: {
    channelData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: false,
      title: '',
      form: {},
      rules: {
        accountId: [{ required: true, message: '请输入渠道账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入渠道密码', trigger: 'blur' }],
        channelType: [{ required: true, message: '请选择渠道种类', trigger: 'blur' }],
      },
    };
  },
  methods: {
    open() {
      this.form = { ...this.channelData };
      this.$refs.form?.resetFields();
      this.visible = true;
      this.title = this.form.id ? '修改渠道' : '新增渠道';
    },
    closeDialog() {
      this.visible = false;
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const api = this.form.id ? updateChannel : addChannel;
          api(this.form)
            .then(() => {
              this.$message.success('操作成功');
              this.$emit('refresh');
              this.closeDialog();
            })
            .catch(() => {
              this.$message.error('操作失败');
            });
        }
      });
    },
  },
};
</script>

<style scoped>
/* 样式 */
</style>
