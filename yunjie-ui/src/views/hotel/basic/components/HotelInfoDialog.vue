<template>
  <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="酒店名称" prop="hotelName">
            <el-input v-model="form.hotelName" placeholder="请输入酒店名称" />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="酒店编码" prop="hotelCode">
            <el-input v-model="form.hotelCode" placeholder="请输入酒店编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="telephone">
            <el-input v-model="form.telephone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="酒店类型" prop="hotelType">
            <el-select v-model="form.hotelType" placeholder="请选择酒店类型" clearable>
              <el-option
                v-for="dict in dict.type.hotel_type"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="酒店星级" prop="starLevel">
            <el-select v-model="form.starLevel" placeholder="请选择酒店星级">
              <el-option v-for="i in 5" :key="i" :label="i + '星'" :value="i" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="省/市/县" prop="provinceCode">
            <el-cascader
              v-if="cascaderShow"
              v-model="cascaderSelect"
              :options="options"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children',
                lazy: true,
                lazyLoad: handleLazyLoad,
              }"
              @change="handleCascaderChange"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="22">
          <el-form-item label="详细地址" prop="detailAddress">
            <el-input id="suggestId" v-model="form.detailAddress" placeholder="请输入详细地址">
              <el-button slot="append" icon="el-icon-location" @click="attachLocation"></el-button>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="map-container" ref="mapContainerRef"></div>

      <el-row>
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input-number disabled v-model="form.longitude" :precision="6" :step="0.000001" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="latitude">
            <el-input-number disabled v-model="form.latitude" :precision="6" :step="0.000001" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="close">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addHotelInfo, updateHotelInfo } from '@/api/hotel/basic';
import { getHotelCityList } from '@/api/dict';
import { watch } from 'vue';
export default {
  name: 'HotelInfoDialog',
  dicts: ['hotel_type'],
  data() {
    return {
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      title: '',
      // 是否是编辑模式
      isEdit: false,
      // 表单参数
      form: {},
      // 省市县选项
      cascaderSelect: [],
      //重新加载下拉框
      cascaderShow: true,
      // 表单校验
      rules: {
        hotelCode: [{ required: true, message: '酒店编码不能为空', trigger: 'blur' }],
        hotelName: [{ required: true, message: '酒店名称不能为空', trigger: 'blur' }],
        provinceCode: [{ required: true, message: '请选择省/市/区', trigger: 'blur' }],
        detailAddress: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
        telephone: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
        longitude: [{ required: true, message: '请在地图上选择指定地点', trigger: 'blur' }],
        latitude: [{ required: true, message: '请在地图上选择指定地点', trigger: 'blur' }],
      },
      //联动选项
      options: [],
      //当前地图
      map: null,
      marker: null,

      //防抖定时器
      timer: null,
    };
  },
  watch: {
    open: {
      handler(newVal, oldVal) {
        if (!newVal) {
          //关闭弹窗时销毁地图
          this.map?.destroy();
          this.map = null;
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 打开弹窗
    openDialog(row, loadingInstance) {
      let selectList = [];
      if (row?.districtCode) {
        selectList = [...new Set([row.provinceCode, row.cityCode, row.districtCode])];
      }
      //强制刷新下拉框
      this.cascaderShow = false;
      this.$nextTick(() => {
        this.cascaderShow = true;
        this.cascaderSelect = selectList;
      });

      setTimeout(() => {
        loadingInstance?.close();
        this.open = true;
        this.initMap();
      }, 1000);

      this.resetForm();

      this.title = row ? '修改酒店信息' : '新增酒店信息';
      if (row) {
        this.form = Object.assign({}, row);
        this.isEdit = true;
      } else {
        this.resetForm();
      }
    },
    close() {
      this.open = false;
    },
    // 提交表单
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            // 修改
            updateHotelInfo(this.form).then((response) => {
              this.$message.success('修改成功');
              this.open = false;
              this.$emit('refresh');
            });
          } else {
            // 新增
            addHotelInfo(this.form).then((response) => {
              this.$message.success('新增成功');
              this.open = false;
              this.$emit('refresh');
            });
          }
        }
      });
    },
    resetForm() {
      this.form = {
        cityCode: undefined,
        detailAddress: undefined,
        districtCode: undefined,
        hotelCode: undefined,
        hotelName: undefined,
        hotelType: undefined,
        latitude: undefined,
        longitude: undefined,
        provinceCode: undefined,
        starLevel: undefined,
        telephone: undefined,
      };
      this.$refs.form?.resetFields();
    },
    //省市县联动
    handleLazyLoad(node, resolve) {
      getHotelCityList(node.value).then((res) => {
        //需要格式化数据lv是3的时候无下级
        let { data } = res;
        data = data.map((item) => ({ ...item, leaf: item.lv === 3 }));
        resolve(data);
      });
    },
    //多选下拉框选中
    handleCascaderChange(res) {
      //存在无市区的省份编码都赋值同一内容
      this.form.provinceCode = res[0];
      this.form.cityCode = res[1] || res[0];
      this.form.districtCode = res[2] || res[0];
    },
    //地图初始化
    initMap() {
      this.$nextTick(() => {
        if (!this.map) {
          const mapContainer = this.$refs.mapContainerRef;
          this.map = new BMapGL.Map(mapContainer);
        }

        let point;
        if (this.form.longitude) {
          point = new BMapGL.Point(this.form.longitude, this.form.latitude);
        } else {
          //默认上海市中心
          point = new BMapGL.Point(121.4737, 31.2304);
        }

        //标记点
        this.marker = new BMapGL.Marker(point, {
          enableDragging: true,
        });
        this.map.addOverlay(this.marker);
        this.map.centerAndZoom(point, 15);
        this.map.enableScrollWheelZoom();

        //拖动绑定事件
        this.marker.addEventListener('dragend', (e) => {
          const newPoint = e.latLng;
          console.log(e);
          this.form.longitude = newPoint.lng;
          this.form.latitude = newPoint.lat;
        });

        //地图点击绑定事件
        this.map.addEventListener('click', (e) => {
          const newPoint = e.latlng;
          this.form.longitude = newPoint.lng;
          this.form.latitude = newPoint.lat;
          this.marker.setPosition(newPoint);
          this.map.centerAndZoom(newPoint, 15);
        });
      });
    },
    //绑定位置
    attachLocation() {
      //检查输入框是否有内容
      if (this.form.detailAddress) {
        let local = new BMapGL.LocalSearch(this.map, {
          //智能搜索
          onSearchComplete: (res) => {
            //非主动选择 暂定第一个位置
            const point = res.getPoi(0).point;
            this.form.longitude = point.lng;
            this.form.latitude = point.lat;
            this.marker.setPosition(point);
            this.map.centerAndZoom(point, 15);
          },
        });

        //防抖功能
        if (this.timer) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.$message.success('已定位到指定地点');
          local.search(this.form.detailAddress);
        }, 300);
      }
    },
  },
};
</script>

<style scoped>
/* 样式 */

.map-container {
  height: 14rem;
  margin: 0 2rem 2rem 2rem;
}
</style>
