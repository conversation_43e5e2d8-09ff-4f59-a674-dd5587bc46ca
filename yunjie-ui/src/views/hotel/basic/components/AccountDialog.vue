<template>
  <el-dialog :title="title" :visible.sync="visible" width="50%" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="员工姓名" prop="name">
        <el-input v-model="form.name" placeholder="请输入员工姓名" />
      </el-form-item>
      <el-form-item label="身份证号" prop="identityCard">
        <el-input v-model="form.identityCard" placeholder="请输入身份证号" />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model="form.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="登录账号" prop="loginName">
        <el-input v-model="form.loginName" placeholder="请输入登录账号" autocomplete="off" />
      </el-form-item>
      <el-form-item label="登录密码" prop="loginPwd">
        <el-input
          v-model="form.loginPwd"
          type="password"
          placeholder="请输入登录密码"
          autocomplete="new-password"
          show-password
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitForm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addHotelAccount, updateHotelAccount } from '@/api/hotel/account';

export default {
  name: 'AccountDialog',
  dicts: ['account_type', 'sys_status'],
  props: {
    accountData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: false,
      title: '',
      form: {},
      rules: {
        identityCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
        loginName: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
        loginPwd: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
        mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        name: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
      },
    };
  },
  methods: {
    open() {
      //打开时进行数据初始化
      this.form = { ...this.accountData };

      this.$refs.form?.resetFields();
      this.visible = true;
      this.title = this.form.id ? '修改账号' : '新增账号';
    },
    closeDialog() {
      this.visible = false;
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const api = this.form.id ? updateHotelAccount : addHotelAccount;
          api(this.form)
            .then(() => {
              this.$message.success('操作成功');
              this.$emit('refresh');
              this.closeDialog();
            })
            .catch(() => {
              this.$message.error('操作失败');
            });
        }
      });
    },
  },
};
</script>

<style scoped>
/* 样式 */
</style>
