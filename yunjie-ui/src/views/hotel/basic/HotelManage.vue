<template>
  <div class="app-container scroll-y">
    <el-form :model="queryParams" size="small" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="酒店名称" prop="hotelName">
        <el-input
          v-model="queryParams.hotelName"
          placeholder="请输入酒店名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="酒店编号" prop="hotelCode">
        <el-input
          v-model="queryParams.hotelCode"
          placeholder="请输入酒店编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.hotel_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery">
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">
          新增酒店
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >
          修改
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          :disabled="multiple"
          size="mini"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="hotelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="酒店编码" align="center" prop="hotelCode" />
      <el-table-column label="酒店名称" align="center" prop="hotelName" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.hotel_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="酒店地址" align="center" prop="detailAddress" />
      <el-table-column label="联系电话" align="center" prop="telephone" />
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">
            修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">
            删除
          </el-button>
          <el-button
            size="mini"
            type="text"
            :class="scope.row.status == '0' ? 'error' : 'success'"
            icon="el-icon-refresh"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status == '0' ? '单位停业' : '单位开业' }}
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-search"
            @click="handleChannel(scope.row)"
          >
            渠道信息
          </el-button>

          <el-button size="mini" type="text" icon="el-icon-user" @click="handleAccount(scope.row)">
            账号信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
    />

    <HotelInfoDialog ref="addRef" @refresh="handleQuery" />
  </div>
</template>

<script>
import {
  getHotelInfoList,
  deleteHotelInfo,
  changeHotelStatus,
  getHotelInfo,
} from '@/api/hotel/basic';
import HotelInfoDialog from './components/HotelInfoDialog.vue';
export default {
  name: 'HotelBasic',
  dicts: ['hotel_status'],
  components: {
    HotelInfoDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 查询参数
      // 分页参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hotelName: undefined,
        hotelCode: undefined,
        status: '-1',
      },
      ids: [],
      names: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 分页
      total: 0,
      // 酒店表格数据
      hotelList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 查询酒店列表
    getList() {
      this.loading = true;
      getHotelInfoList(this.queryParams)
        .then((response) => {
          this.hotelList = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch(() => {
          this.hotelList = [];
          this.total = 0;
          this.loading = false;
        });
    },
    // 搜索按钮操作
    handleQuery() {
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    //多选
    handleSelectionChange(selection) {
      const { ids, names } = selection.reduce(
        (acc, item) => ({
          ids: [...acc.ids, item.id],
          names: [...acc.names, item.hotelName],
        }),
        { ids: [], names: [] }
      );
      this.ids = ids;
      this.names = names;
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    //新增酒店信息
    handleAdd() {
      this.$refs.addRef.openDialog();
    },
    //修改酒店信息
    handleUpdate(row) {
      const loadingInstance = this.$loading({
        lock: true,
        text: '信息加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(255, 255, 255, 0.8)',
      });
      //加载最新酒店信息
      getHotelInfo(row.id == null ? this.ids[0] : row.id)
        .then((res) => {
          this.$refs.addRef.openDialog(res?.data, loadingInstance);
        })
        .catch(() => {
          loadingInstance.close();
        });
    },
    // 删除按钮操作
    handleDelete(row) {
      const names = row.hotelName || this.names;
      this.$confirm('确认要删除' + names + '吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return deleteHotelInfo(row.id || this.ids);
        })
        .then(() => {
          this.getList();
          this.$message.success('删除成功');
        })
        .catch(() => {});
    },
    // 修改状态
    handleStatusChange(row) {
      let text = row.status === 0 ? '停业' : '开业';
      this.$confirm(`确认要将${row.hotelName}设置为${text}状态吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return changeHotelStatus(row.id, row.status === 1 ? 0 : 1);
        })
        .then(() => {
          this.getList();
          this.$message.success(text + '成功');
        })
        .catch(() => {});
    },
    //处理渠道信息
    handleChannel(row) {
      this.$emit('showChannelDetail', row);
    },
    //处理用户信息
    handleAccount(row) {
      this.$emit('showAccountDetail', row);
    },
  },
};
</script>

<style scoped></style>
