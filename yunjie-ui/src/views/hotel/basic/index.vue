<template>
  <div class="container">
    <el-carousel
      ref="carousel"
      class="el-carousel"
      arrow="never"
      height="100%"
      :autoplay="false"
      :loop="false"
      indicator-position="none"
    >
      <el-carousel-item :key="0">
        <HotelManage
          @showChannelDetail="showChannelDetail"
          @showAccountDetail="showAccountDetail"
        />
      </el-carousel-item>
      <el-carousel-item :key="1">
        <HotelChannel ref="hotelChannelRef" :hotel="selectedHotel" @goBack="goBack" />
      </el-carousel-item>
      <el-carousel-item :key="2">
        <HotelAccount ref="hotelAccountRef" :hotel="selectedHotel" @goBack="goBack" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
import HotelManage from './HotelManage.vue';
import HotelChannel from './HotelChannel.vue';
import HotelAccount from './HotelAccount.vue';

export default {
  components: {
    HotelManage,
    HotelChannel,
    HotelAccount,
  },
  data() {
    return {
      selectedHotel: {},
    };
  },
  methods: {
    goBack() {
      this.$refs.carousel.setActiveItem(0);
    },
    showChannelDetail(row) {
      //查看详情
      this.selectedHotel = row;
      this.$nextTick(() => {
        this.$refs.carousel.setActiveItem(1);
        this.$refs.hotelChannelRef.resetQuery();
      });
    },
    showAccountDetail(row) {
      this.selectedHotel = row;
      this.$nextTick(() => {
        this.$refs.carousel.setActiveItem(2);
        this.$refs.hotelAccountRef.resetQuery();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  display: flow-root;
  height: 100%;
  position: absolute;
  width: 100%;

  .el-carousel {
    height: 100%;
  }
}
</style>
