#!/bin/bash

# Ubuntu服务器Docker修复命令集合
# 请在Ubuntu服务器上逐行执行这些命令

echo "=========================================="
echo "Ubuntu服务器Docker修复开始"
echo "=========================================="

# 1. 更新系统包
echo "1. 更新系统包..."
sudo apt update

# 2. 卸载旧版本Docker（如果存在）
echo "2. 卸载旧版本Docker..."
sudo apt remove -y docker docker-engine docker.io containerd runc 2>/dev/null || true

# 3. 安装必要的包
echo "3. 安装必要的包..."
sudo apt install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# 4. 添加Docker官方GPG密钥
echo "4. 添加Docker官方GPG密钥..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 5. 添加Docker仓库
echo "5. 添加Docker仓库..."
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 6. 更新包索引
echo "6. 更新包索引..."
sudo apt update

# 7. 安装Docker
echo "7. 安装Docker..."
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 8. 启动Docker服务
echo "8. 启动Docker服务..."
sudo systemctl start docker
sudo systemctl enable docker

# 9. 添加当前用户到docker组
echo "9. 添加用户到docker组..."
sudo groupadd -f docker
sudo usermod -aG docker $USER

# 10. 修改docker.sock权限（临时解决方案）
echo "10. 修改docker.sock权限..."
sudo chmod 666 /var/run/docker.sock

# 11. 安装Docker Compose（独立版本）
echo "11. 安装Docker Compose..."
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose

# 12. 验证安装
echo "12. 验证Docker安装..."
echo "Docker版本:"
docker --version
echo "Docker Compose版本:"
docker-compose --version

# 13. 测试Docker
echo "13. 测试Docker..."
if docker run --rm hello-world; then
    echo "✅ Docker安装和配置成功！"
else
    echo "❌ Docker测试失败，可能需要重新登录或重启"
fi

# 14. 显示Docker状态
echo "14. Docker服务状态:"
sudo systemctl status docker --no-pager

echo ""
echo "=========================================="
echo "Docker修复完成！"
echo ""
echo "如果仍然遇到权限问题，请执行："
echo "sudo chmod 666 /var/run/docker.sock"
echo "或者注销重新登录以刷新用户组权限"
echo "=========================================="
