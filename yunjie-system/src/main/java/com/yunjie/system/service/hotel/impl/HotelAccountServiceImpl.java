package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.utils.AESUtil;
import com.yunjie.common.utils.SecurityUtils;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelAccount;
import com.yunjie.system.mapper.hotel.HotelAccountMapper;
import com.yunjie.system.model.request.hotel.account.*;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.response.hotel.GetHotelAccountResponse;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.service.hotel.HotelAccountService;
import com.yunjie.system.service.hotel.HotelInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelAccountServiceImpl implements HotelAccountService {

    @Resource
    private HotelAccountMapper hotelAccountMapper;

    @Resource
    private HotelInfoService hotelInfoService;

    @Value("${aes.key}")
    private String aesKey;

    @Value("${aes.iv}")
    private String aesIv;

    @Override
    public int insertHotelAccount(InsertHotelAccountRequest request) {
        try {
            HotelAccount hotelAccount = new HotelAccount();
            BeanUtils.copyBeanProp(hotelAccount, request);

            hotelAccount.setLoginPwd(AESUtil.encrypt(request.getLoginPwd(), aesKey, aesIv));
            hotelAccount.setMobile(AESUtil.encrypt(request.getMobile(), aesKey, aesIv));
            hotelAccount.setIdentityCard(AESUtil.encrypt(request.getIdentityCard(), aesKey, aesIv));
            hotelAccount.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            hotelAccount.setCreateTime(new Date());
            hotelAccount.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
            hotelAccount.setUpdateTime(new Date());
            return hotelAccountMapper.insert(hotelAccount);
        } catch (Exception e) {
            log.error("新增酒店账号信息失败:{}", e.getMessage());
        }
        return 0;
    }

    @Override
    public int updateHotelAccount(UpdateHotelAccountRequest request) {
        try {
            LambdaUpdateWrapper<HotelAccount> updateWrapper = new LambdaUpdateWrapper<>();
            if (StringUtils.isNoneBlank(request.getLoginName())) {
                updateWrapper.set(HotelAccount::getLoginName, request.getLoginName());
            }
            if (StringUtils.isNoneBlank(request.getLoginPwd())) {
                updateWrapper.set(HotelAccount::getLoginPwd, AESUtil.encrypt(request.getLoginPwd(), aesKey, aesIv));
            }
            if (StringUtils.isNoneBlank(request.getName())) {
                updateWrapper.set(HotelAccount::getName, request.getName());
            }
            if (StringUtils.isNoneBlank(request.getMobile())) {
                updateWrapper.set(HotelAccount::getMobile, AESUtil.encrypt(request.getMobile(), aesKey, aesIv));
            }
            if (StringUtils.isNoneBlank(request.getIdentityCard())) {
                updateWrapper.set(HotelAccount::getIdentityCard, AESUtil.encrypt(request.getIdentityCard(), aesKey, aesIv));
            }
            updateWrapper.set(HotelAccount::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
            updateWrapper.set(HotelAccount::getUpdateTime, new Date());
            updateWrapper.eq(HotelAccount::getId, request.getId());
            return hotelAccountMapper.update(null, updateWrapper);
        } catch (Exception e) {
            log.error("更新酒店账号信息失败:{}", e.getMessage());
        }
        return 0;
    }

    @Override
    public int changeStatus(ChangeHotelAccountStatusRequest request) {
        LambdaUpdateWrapper<HotelAccount> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HotelAccount::getStatus, request.getStatus());
        updateWrapper.set(HotelAccount::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelAccount::getUpdateTime, new Date());
        updateWrapper.eq(HotelAccount::getId, request.getId());
        return hotelAccountMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteHotelAccount(DeleteHotelAccountRequest request) {
        request.setUserName(SecurityUtils.getLoginUser().getUsername());
        return hotelAccountMapper.deleteHotelAccountByIds(request);
    }

    @Override
    public GetHotelAccountResponse getHotelAccount(GetHotelAccountRequest request) {
        try {
            LambdaQueryWrapper<HotelAccount> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotelAccount::getId, request.getId());
            queryWrapper.eq(HotelAccount::getDelFlag, 0);
            HotelAccount hotelAccount = hotelAccountMapper.selectOne(queryWrapper);
            if (hotelAccount != null) {
                GetHotelAccountResponse response = new GetHotelAccountResponse();
                BeanUtils.copyBeanProp(response, hotelAccount);

                // 获取酒店基本信息数据
                GetHotelInfoRequest getHotelInfoRequest = new GetHotelInfoRequest();
                getHotelInfoRequest.setId(hotelAccount.getHotelId());
                GetHotelInfoResponse getHotelInfoResponse = hotelInfoService.getHotelInfo(getHotelInfoRequest);
                if (getHotelInfoResponse != null) {
                    response.setHotelCode(getHotelInfoResponse.getHotelCode());
                    response.setHotelName(getHotelInfoResponse.getHotelName());
                }

                response.setLoginPwd(AESUtil.decrypt(hotelAccount.getLoginPwd(), aesKey, aesIv));
                response.setMobile(AESUtil.decrypt(hotelAccount.getMobile(), aesKey, aesIv));
                response.setIdentityCard(AESUtil.decrypt(hotelAccount.getIdentityCard(), aesKey, aesIv));
                return response;
            }
        } catch (Exception e) {
            log.error("获取酒店账号信息失败:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<GetHotelAccountResponse> getHotelAccountList(GetHotelAccountListRequest request) {
        try {
            if (StringUtils.isNoneBlank(request.getMobile())) {
                request.setMobile(AESUtil.encrypt(request.getMobile(), aesKey, aesIv));
            }

            List<GetHotelAccountResponse> list = hotelAccountMapper.getHotelAccountList(request);
            list.forEach(x -> {
                try {
                    x.setLoginPwd(AESUtil.decrypt(x.getLoginPwd(), aesKey, aesIv));
                    x.setMobile(AESUtil.decrypt(x.getMobile(), aesKey, aesIv));
                    x.setIdentityCard(AESUtil.decrypt(x.getIdentityCard(), aesKey, aesIv));
                } catch (Exception e) {
                    log.error("获取酒店账号信息列表敏感字段解密失败:{}", e.getMessage());
                }
            });
            return list;
        } catch (Exception e) {
            log.error("获取酒店账号信息列表失败:{}", e.getMessage());
        }
        return null;
    }
}
