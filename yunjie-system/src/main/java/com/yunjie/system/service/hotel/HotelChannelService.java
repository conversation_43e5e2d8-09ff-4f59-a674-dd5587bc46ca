package com.yunjie.system.service.hotel;

import com.yunjie.system.model.request.hotel.channel.*;
import com.yunjie.system.model.response.hotel.GetHotelChannelResponse;

import java.util.List;

public interface HotelChannelService {

    /**
     * 新增酒店渠道信息
     * @param request
     * @return
     */
    int insertHotelChannel(InsertHotelChannelRequest request);

    /**
     * 更新酒店渠道信息
     * @param request
     * @return
     */
    int updateHotelChannel(UpdateHotelChannelRequest request);

    /**
     * 变更酒店渠道信息状态
     * @param request
     * @return
     */
    int changeStatus(ChangeHotelChannelStatusRequest request);

    /**
     * 删除酒店渠道信息
     * @param request
     * @return
     */
    int deleteHotelChannel(DeleteHotelChannelRequest request);

    /**
     * 获取酒店渠道信息
     * @param request
     * @return
     */
    GetHotelChannelResponse getHotelChannel(GetHotelChannelRequest request);

    /**
     * 获取酒店渠道信息列表
     * @param request
     * @return
     */
    List<GetHotelChannelResponse> getHotelChannelList(GetHotelChannelListRequest request);
}
