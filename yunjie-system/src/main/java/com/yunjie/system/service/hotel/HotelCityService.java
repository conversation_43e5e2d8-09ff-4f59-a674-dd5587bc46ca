package com.yunjie.system.service.hotel;

import com.yunjie.system.model.request.hotel.city.GetHotelCityListRequest;
import com.yunjie.system.model.response.hotel.GetHotelCityListResponse;

import java.util.List;

public interface HotelCityService {

    /**
     * 获取省市区列表数据
     * @param request
     * @return
     */
    List<GetHotelCityListResponse> getHotelCityList(GetHotelCityListRequest request);

    /**
     * 递归获取省市区列表数据
     * @return
     */
    List<GetHotelCityListResponse> getHotelCityListBySelect();

}
