package com.yunjie.system.service.wechat.impl;

import com.alibaba.fastjson2.JSON;
import com.yunjie.common.constant.WechatConstants;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.redis.RedisCache;
import com.yunjie.common.utils.JwtUtil;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.common.utils.http.HttpUtil;
import com.yunjie.system.model.request.hotel.user.GetHotelUserRequest;
import com.yunjie.system.model.request.hotel.user.InsertHotelUserRequest;
import com.yunjie.system.model.request.hotel.user.UpdateHotelUserRequest;
import com.yunjie.system.model.request.wechat.*;
import com.yunjie.system.model.response.hotel.GetHotelUserResponse;
import com.yunjie.system.model.response.hotel.HotelUserResponse;
import com.yunjie.system.model.response.wechat.*;
import com.yunjie.system.service.hotel.HotelUserService;
import com.yunjie.system.service.wechat.WechatBaseInfoService;
import com.yunjie.system.service.wechat.WechatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WechatServiceImpl implements WechatService {

    @Resource
    private HotelUserService hotelUserService;

    @Resource
    private WechatBaseInfoService wechatBaseInfoService;

    @Resource
    private RedisCache redisCache;

    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public R<HotelUserResponse> wechatLogin(WechatLoginRequest request) {
        try {
            GetWechatBaseInfoRequest getWechatBaseInfoRequest = new GetWechatBaseInfoRequest();
            getWechatBaseInfoRequest.setAppId(request.getAppId());
            GetWechatBaseInfoResponse getWechatBaseInfoResponse = wechatBaseInfoService.getWechatBaseInfo(getWechatBaseInfoRequest);
            if (getWechatBaseInfoResponse == null) {
                log.error("微信小程序登录失败,应用程序未配置,appId:{}", request.getAppId());
                return R.fail("微信小程序登录失败,应用程序未配置");
            }

            WxCode2SessionResponse wxCode2SessionResponse = new WxCode2SessionResponse();

            // 获取小程序登录信息
            Map<String, String> bodyMap = new HashMap();
            bodyMap.put("appid", getWechatBaseInfoResponse.getAppId());
            bodyMap.put("secret", getWechatBaseInfoResponse.getAppSecret());
            bodyMap.put("js_code", request.getJsCode());
            bodyMap.put("grant_type", "authorization_code");
            String codeSession = "";
            if (StringUtils.isNoneBlank(env) && env.equals("pro")) {
                codeSession = HttpUtil.doGet(WechatConstants.JSCODE_URL, bodyMap, null);
            } else {
                codeSession = "{\"openid\":\"oLbyJ7bk7JVNciBDF-a-XsDsLNA4\",\"session_key\":\"xxxxx\",\"unionid\":\"xxxxx\",\"errcode\":0,\"errmsg\":\"\"}";
            }

            if (StringUtils.isNoneBlank(codeSession)) {
                wxCode2SessionResponse = JSON.parseObject(codeSession, WxCode2SessionResponse.class);

                // 获取用户信息
                if (wxCode2SessionResponse != null && (wxCode2SessionResponse.getErrcode() == null || wxCode2SessionResponse.getErrcode().equals(0))) {

                    HotelUserResponse hotelUserResponse = new HotelUserResponse();
                    hotelUserResponse.setOpenid(wxCode2SessionResponse.getOpenid());
                    hotelUserResponse.setUnionId(wxCode2SessionResponse.getUnionid());

                    GetHotelUserRequest getHotelUserRequest = new GetHotelUserRequest();
                    getHotelUserRequest.setOpenid(wxCode2SessionResponse.getOpenid());
                    GetHotelUserResponse getHotelUserResponse = hotelUserService.getHotelUser(getHotelUserRequest);
                    if (getHotelUserResponse != null) {
                        hotelUserResponse.setUserId(getHotelUserResponse.getUserId());
                        hotelUserResponse.setNickName(getHotelUserResponse.getNickName());
                        hotelUserResponse.setAvatarUrl(getHotelUserResponse.getAvatarUrl());
                        hotelUserResponse.setMobile(getHotelUserResponse.getMobile());
                        hotelUserResponse.setStatus(getHotelUserResponse.getStatus());
                        hotelUserResponse.setCreateTime(getHotelUserResponse.getCreateTime());

                        loginCache(hotelUserResponse);
                    } else {
                        // 注册用户信息
                        InsertHotelUserRequest insertHotelUserRequest = new InsertHotelUserRequest();
                        insertHotelUserRequest.setOpenid(wxCode2SessionResponse.getOpenid());
                        insertHotelUserRequest.setUnionId(wxCode2SessionResponse.getUnionid());
                        insertHotelUserRequest.setAppId(request.getAppId());
                        insertHotelUserRequest.setBindFlag(0);
                        R<GetHotelUserResponse> result = hotelUserService.insertHotelUser(insertHotelUserRequest);
                        if (result.getCode() == 200 && result.getData() != null) {
                            getHotelUserResponse = result.getData();

                            hotelUserResponse.setUserId(getHotelUserResponse.getUserId());
                            hotelUserResponse.setNickName(getHotelUserResponse.getNickName());
                            hotelUserResponse.setAvatarUrl(getHotelUserResponse.getAvatarUrl());
                            hotelUserResponse.setMobile(getHotelUserResponse.getMobile());
                            hotelUserResponse.setStatus(getHotelUserResponse.getStatus());
                            hotelUserResponse.setCreateTime(getHotelUserResponse.getCreateTime());

                            loginCache(hotelUserResponse);
                        }
                    }
                    log.info(String.format("%s%s", "wxLogin登录信息:", JSON.toJSONString(wxCode2SessionResponse)));
                    return R.ok(hotelUserResponse);
                }
            }

            log.info("登录失败,获取微信信息失败:{}", JSON.toJSONString(wxCode2SessionResponse));
            return R.fail(wxCode2SessionResponse.getErrmsg());
        } catch (Exception e) {
            log.error("获取微信小程序登录信息异常:{}", e.getMessage());
            return R.fail("网络异常请稍后再试");
        }
    }

    @Override
    public R<GetUserPhoneResponse> getUserPhone(GetUserPhoneRequest request) {
        try {
            GetWechatBaseInfoRequest getWechatBaseInfoRequest = new GetWechatBaseInfoRequest();
            getWechatBaseInfoRequest.setAppId(request.getAppId());
            GetWechatBaseInfoResponse getWechatBaseInfoResponse = wechatBaseInfoService.getWechatBaseInfo(getWechatBaseInfoRequest);
            if (getWechatBaseInfoResponse == null) {
                log.error("获取用户手机号失败,应用程序未配置,appId:{}", request.getAppId());
                return R.fail("获取用户手机号失败,应用程序未配置");
            }

            if (StringUtils.isNoneBlank(env) && env.equals("pro")) {
                // 获取access_token
                GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest();
                getAccessTokenRequest.setAppId(request.getAppId());
                R<GetAccessTokenResponse> result = getAccessToken(getAccessTokenRequest);
                if (result != null && result.getCode() == 200 && result.getData() != null) {

                    GetUserPhoneResponse getUserPhoneResponse = new GetUserPhoneResponse();

                    Map<String, String> bodyMap = new HashMap();
                    bodyMap.put("code", request.getCode());
                    String phoneInfo = HttpUtil.httpPost(String.format("%s%s", WechatConstants.GET_PHONE_URL, "?access_token=" + result.getData().getAccess_token()), bodyMap, null);

                    if (StringUtils.isNoneBlank(phoneInfo)) {
                        getUserPhoneResponse = JSON.parseObject(phoneInfo, GetUserPhoneResponse.class);

                        if (!getUserPhoneResponse.getErrCode().equals(0) && getUserPhoneResponse.getPhoneInfo() == null) {
                            log.info("授权获取手机号失败,信息:{}", getUserPhoneResponse.getErrMsg());
                            return R.fail("获取用户手机号失败");
                        }
                        return R.ok(getUserPhoneResponse);
                    }
                }
            } else {
                GetUserPhoneResponse getUserPhoneResponse = new GetUserPhoneResponse();
                getUserPhoneResponse.setErrCode(0);
                getUserPhoneResponse.setErrMsg("");

                PhoneInfo phoneInfo = new PhoneInfo();
                phoneInfo.setPhoneNumber("18852083159");
                phoneInfo.setPurePhoneNumber("18852083159");
                phoneInfo.setCountryCode("86");
                getUserPhoneResponse.setPhoneInfo(phoneInfo);

                return R.ok(getUserPhoneResponse);
            }

            log.error("获取用户手机号失败,原因获取access_token失败,appId:{}", request.getAppId());
            return R.fail("获取用户手机号失败");
        } catch (Exception e) {
            log.error("获取用户手机号异常:{}", e.getMessage());
            return R.fail("网络异常请稍后再试");
        }
    }

    @Override
    public R<HotelUserResponse> authPhoneRegister(AuthPhoneRequest request) {
        log.info(String.format("%s%s", "授权手机号注册,入参:", JSON.toJSONString(request)));
        int edit = 0;

        // 根据手机号和appid获取用户信息
        GetHotelUserRequest getHotelUserRequest = new GetHotelUserRequest();
        getHotelUserRequest.setAppId(request.getAppId());
        getHotelUserRequest.setMobile(request.getMobile());
        GetHotelUserResponse getHotelUserResponse = hotelUserService.getHotelUser(getHotelUserRequest);
        if (getHotelUserResponse == null) {
            // 根据openid和unionid获取用户信息
            getHotelUserRequest = new GetHotelUserRequest();
            getHotelUserRequest.setOpenid(request.getOpenId());
            getHotelUserRequest.setUnionId(request.getUnionId());
            getHotelUserResponse = hotelUserService.getHotelUser(getHotelUserRequest);
        }

        if (getHotelUserResponse != null) {
            // 更新用户手机号
            UpdateHotelUserRequest updateHotelUserRequest = new UpdateHotelUserRequest();
            updateHotelUserRequest.setMobile(request.getMobile());
            updateHotelUserRequest.setOpenid(request.getOpenId());
            updateHotelUserRequest.setAppId(request.getAppId());
            updateHotelUserRequest.setUserId(getHotelUserResponse.getUserId());
            edit = hotelUserService.updateHotelUser(updateHotelUserRequest);
        } else {
            // 注册用户信息
            InsertHotelUserRequest insertHotelUserRequest = new InsertHotelUserRequest();
            insertHotelUserRequest.setOpenid(request.getOpenId());
            insertHotelUserRequest.setUnionId(request.getUnionId());
            insertHotelUserRequest.setAppId(request.getAppId());
            insertHotelUserRequest.setMobile(request.getMobile());
            R<GetHotelUserResponse> result = hotelUserService.insertHotelUser(insertHotelUserRequest);
            if (result.getCode() == 200 && result.getData() != null) {
                getHotelUserResponse = result.getData();
                edit = 1;
            }
        }

        if (edit > 0) {
            getHotelUserResponse.setMobile(request.getMobile());
            HotelUserResponse hotelUserResponse = new HotelUserResponse();
            BeanUtils.copyBeanProp(hotelUserResponse, getHotelUserResponse);

            loginCache(hotelUserResponse);

            return R.ok(hotelUserResponse);
        }

        return R.fail("授权手机号注册失败");
    }

    @Override
    public R<GetAccessTokenResponse> getAccessToken(GetAccessTokenRequest request) {
        try {
            GetWechatBaseInfoRequest getWechatBaseInfoRequest = new GetWechatBaseInfoRequest();
            getWechatBaseInfoRequest.setAppId(request.getAppId());
            GetWechatBaseInfoResponse getWechatBaseInfoResponse = wechatBaseInfoService.getWechatBaseInfo(getWechatBaseInfoRequest);
            if (getWechatBaseInfoResponse == null) {
                log.error("获取access_token失败,应用程序未配置,appId:{}", request.getAppId());
                return R.fail("获取access_token失败,应用程序未配置");
            }

            GetAccessTokenResponse tokenResponse;

            String cacheKey = "getAccessToken_by_cache_" + getWechatBaseInfoResponse.getAppId();
            tokenResponse = redisCache.getCacheObject(cacheKey);
            if (tokenResponse == null) {
                // 微信端获取access_token
                Map<String, String> bodyMap = new HashMap();
                bodyMap.put("appid", getWechatBaseInfoResponse.getAppId());
                bodyMap.put("secret", getWechatBaseInfoResponse.getAppSecret());
                bodyMap.put("grant_type", "client_credential");
                String tokenInfo = HttpUtil.doGet(WechatConstants.GET_ACCESSTOKEN_URL, bodyMap, null);

                if (StringUtils.isNoneBlank(tokenInfo)) {
                    tokenResponse = JSON.parseObject(tokenInfo, GetAccessTokenResponse.class);

                    // access_token缓存7100秒
                    redisCache.setCacheObject(cacheKey, tokenResponse, 7100, TimeUnit.SECONDS);

                    return R.ok(tokenResponse);
                }
                return R.fail("获取access_token失败");
            }

            return R.ok(tokenResponse);
        } catch (Exception e) {
            log.error("获取access_token异常:{}", e.getMessage());
            return R.fail("网络异常请稍后再试");
        }
    }

    /**
     * 用户登录验证
     *
     * @param response
     * @return
     */
    private HotelUserResponse loginCache(HotelUserResponse response) {
        //缓存用户登录信息
        response.setToken(JwtUtil.generateToken(response.getUserId().toString()));
        return response;
    }
}
