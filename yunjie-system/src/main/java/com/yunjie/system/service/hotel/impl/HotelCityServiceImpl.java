package com.yunjie.system.service.hotel.impl;

import com.yunjie.system.mapper.hotel.HotelCityMapper;
import com.yunjie.system.model.request.hotel.city.GetHotelCityListRequest;
import com.yunjie.system.model.response.hotel.GetHotelCityListResponse;
import com.yunjie.system.service.hotel.HotelCityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class HotelCityServiceImpl implements HotelCityService {

    @Resource
    private HotelCityMapper hotelCityMapper;

    @Override
    public List<GetHotelCityListResponse> getHotelCityList(GetHotelCityListRequest request) {
        return hotelCityMapper.getHotelCityList(request);
    }

    @Override
    public List<GetHotelCityListResponse> getHotelCityListBySelect() {
        return buildRegionTree(hotelCityMapper.getHotelCityList(new GetHotelCityListRequest()), 0L);
    }

    /**
     * 递归获取省市区数据
     * @param allRegions
     * @param parentId
     * @return
     */
    public List<GetHotelCityListResponse> buildRegionTree(List<GetHotelCityListResponse> allRegions, Long parentId) {
        List<GetHotelCityListResponse> result = new ArrayList<>();
        for (GetHotelCityListResponse region : allRegions) {
            if (region.getPid().equals(parentId)) {
                List<GetHotelCityListResponse> children = buildRegionTree(allRegions, Long.parseLong(region.getId()));
                region.setChildren(children);
                result.add(region);
            }
        }
        return result;
    }
}
