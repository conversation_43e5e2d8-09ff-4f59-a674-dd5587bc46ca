package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.utils.AESUtil;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelBooking;
import com.yunjie.system.mapper.hotel.HotelBookingMapper;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingListRequest;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingRequest;
import com.yunjie.system.model.request.hotel.booking.InsertHotelBookingRequest;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.response.hotel.GetHotelBookingResponse;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.service.hotel.HotelBookingService;
import com.yunjie.system.service.hotel.HotelInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelBookingServiceImpl implements HotelBookingService {

    @Resource
    private HotelBookingMapper hotelBookingMapper;

    @Resource
    private HotelInfoService hotelInfoService;

    @Value("${aes.key}")
    private String aesKey;

    @Value("${aes.iv}")
    private String aesIv;

    @Override
    public R<GetHotelBookingResponse> insertHotelBooking(InsertHotelBookingRequest request) {
        try {
            HotelBooking hotelBooking = new HotelBooking();
            BeanUtils.copyBeanProp(hotelBooking, request);

            if (StringUtils.isNoneBlank(request.getContactPhone())) {
                hotelBooking.setContactPhone(AESUtil.encrypt(request.getContactPhone(), aesKey, aesIv));
            }

            hotelBooking.setStatus(1);
            hotelBooking.setCreateBy(request.getUserId().toString());
            hotelBooking.setCreateTime(new Date());
            hotelBooking.setUpdateBy(request.getUserId().toString());
            hotelBooking.setUpdateTime(new Date());
            int count = hotelBookingMapper.insert(hotelBooking);
            if (count > 0) {
                GetHotelBookingResponse response = new GetHotelBookingResponse();

                BeanUtils.copyBeanProp(response, hotelBooking);

                if (StringUtils.isNoneBlank(hotelBooking.getContactPhone())) {
                    response.setContactPhone(AESUtil.decrypt(hotelBooking.getContactPhone(), aesKey, aesIv));
                }

                return R.ok(response);
            }
        } catch (Exception e) {
            log.error("新增酒店预订信息异常:{}", e.getMessage());
        }
        return R.fail("预订失败");
    }

    @Override
    public GetHotelBookingResponse getHotelBooking(GetHotelBookingRequest request) {
        try {
            LambdaQueryWrapper<HotelBooking> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotelBooking::getId, request.getId());
            if (request.getUserId() != null && request.getUserId() > 0) {
                queryWrapper.eq(HotelBooking::getUserId, request.getUserId());
            }
            HotelBooking hotelBooking = hotelBookingMapper.selectOne(queryWrapper);
            if (hotelBooking != null) {
                GetHotelBookingResponse response = new GetHotelBookingResponse();
                BeanUtils.copyBeanProp(response, hotelBooking);

                if (StringUtils.isNoneBlank(hotelBooking.getContactPhone())) {
                    response.setContactPhone(AESUtil.decrypt(hotelBooking.getContactPhone(), aesKey, aesIv));
                }

                // 获取酒店基本信息数据
                GetHotelInfoRequest getHotelInfoRequest = new GetHotelInfoRequest();
                getHotelInfoRequest.setId(hotelBooking.getHotelId());
                GetHotelInfoResponse getHotelInfoResponse = hotelInfoService.getHotelInfo(getHotelInfoRequest);
                if (getHotelInfoResponse != null) {
                    response.setHotelName(getHotelInfoResponse.getHotelName());
                }

                return response;
            }
        } catch (Exception e) {
            log.error("获取酒店预订信息失败:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<GetHotelBookingResponse> getHotelBookingList(GetHotelBookingListRequest request) {
        List<GetHotelBookingResponse> list = hotelBookingMapper.getHotelBookingList(request);
        list.forEach(x -> {
            try {
                if (StringUtils.isNoneBlank(x.getContactPhone())) {
                    x.setContactPhone(AESUtil.decrypt(x.getContactPhone(), aesKey, aesIv));
                }
            } catch (Exception e) {
                log.error("获取酒店预订信息列表敏感字段解密失败:{}", e.getMessage());
            }
        });
        return list;
    }
}
