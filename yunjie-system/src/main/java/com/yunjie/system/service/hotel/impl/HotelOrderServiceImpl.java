package com.yunjie.system.service.hotel.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.utils.AESUtil;
import com.yunjie.common.utils.DateUtils;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelOrder;
import com.yunjie.system.domain.sms.SmsRecord;
import com.yunjie.system.mapper.hotel.HotelOrderMapper;
import com.yunjie.system.mapper.sms.SmsRecordMapper;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderListRequest;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderRequest;
import com.yunjie.system.model.request.hotel.order.InsertHotelOrderRequest;
import com.yunjie.system.model.request.hotel.order.UpdateHotelOrderRequest;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.model.response.hotel.GetHotelOrderResponse;
import com.yunjie.system.service.hotel.HotelInfoService;
import com.yunjie.system.service.hotel.HotelOrderService;
import com.yunjie.system.service.sms.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelOrderServiceImpl implements HotelOrderService {

    @Resource
    private HotelOrderMapper hotelOrderMapper;

    @Resource
    private SmsRecordMapper smsRecordMapper;

    @Resource
    private HotelInfoService hotelInfoService;

    @Resource
    private SmsService smsService;

    @Resource
    private Snowflake snowflake;

    @Value("${aes.key}")
    private String aesKey;

    @Value("${aes.iv}")
    private String aesIv;

    @Override
    public R insertHotelOrder(InsertHotelOrderRequest request) {
        try {
            LambdaQueryWrapper<HotelOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotelOrder::getOutNo, request.getOutNo());
            long count = hotelOrderMapper.selectCount(queryWrapper);
            if (count > 0) {
                return R.fail("订单已存在");
            }

            HotelOrder hotelOrder = new HotelOrder();
            BeanUtils.copyBeanProp(hotelOrder, request);

            if (StringUtils.isNoneBlank(request.getOrderTel())) {
                hotelOrder.setOrderTel(AESUtil.encrypt(request.getOrderTel(), aesKey, aesIv));
            }

            hotelOrder.setOrderFlag(1);
            hotelOrder.setOrderNo(DateUtils.dateTime() + snowflake.nextIdStr());
            hotelOrder.setOrderStatus(1);
            hotelOrder.setIsVirtual(0);
            hotelOrder.setEnable(1);
            hotelOrder.setDeleteFlag(1);
            hotelOrder.setCreatedAt(new Date());
            hotelOrder.setCreateBy(request.getOrderName());
            hotelOrder.setUpdatedBy(request.getOrderName());
            hotelOrder.setUpdatedAt(new Date());
            hotelOrder.setGuestSource(0);
            hotelOrder.setPushStatus(1);
            hotelOrder.setSmsStatus(1);

            int countTwo = hotelOrderMapper.insert(hotelOrder);
            if (countTwo > 0) {
                return R.ok();
            }
            return R.fail();
        } catch (Exception e) {
            log.error("新增酒店订单信息异常:{}", e.getMessage());
            return R.fail();
        }
    }

    @Override
    public R updateHotelOrder(UpdateHotelOrderRequest request) {
        LambdaUpdateWrapper<HotelOrder> updateWrapper = new LambdaUpdateWrapper<>();
        if (request.getOrderStatus() != null && request.getOrderStatus() == 0) {
            updateWrapper.set(HotelOrder::getOrderStatus, 0);
        }
        updateWrapper.set(HotelOrder::getUpdatedBy, request.getOrderName());
        updateWrapper.set(HotelOrder::getUpdatedAt, new Date());
        updateWrapper.eq(HotelOrder::getOrderChannel, request.getOrderChannel());
        updateWrapper.eq(HotelOrder::getHotelCode, request.getHotelCode());
        updateWrapper.eq(HotelOrder::getOutNo, request.getOutNo());
        int count = hotelOrderMapper.update(null, updateWrapper);
        if (count > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @Override
    public GetHotelOrderResponse getHotelOrder(GetHotelOrderRequest request) {
        try {
            LambdaQueryWrapper<HotelOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotelOrder::getId, request.getId());
            if (StringUtils.isNoneBlank(request.getMobile())) {
                queryWrapper.eq(HotelOrder::getOrderTel, request.getMobile());
            }
            queryWrapper.eq(HotelOrder::getDeleteFlag, 1);
            HotelOrder hotelOrder = hotelOrderMapper.selectOne(queryWrapper);
            if (hotelOrder != null) {
                GetHotelOrderResponse response = new GetHotelOrderResponse();
                BeanUtils.copyBeanProp(response, hotelOrder);

                if (StringUtils.isNoneBlank(hotelOrder.getOrderTel())) {
                    response.setOrderTel(AESUtil.decrypt(hotelOrder.getOrderTel(), aesKey, aesIv));
                }

                // 获取酒店基本信息数据
                GetHotelInfoRequest getHotelInfoRequest = new GetHotelInfoRequest();
                getHotelInfoRequest.setId(hotelOrder.getHotelId().longValue());
                GetHotelInfoResponse getHotelInfoResponse = hotelInfoService.getHotelInfo(getHotelInfoRequest);
                if (getHotelInfoResponse != null) {
                    response.setHotelName(getHotelInfoResponse.getHotelName());
                }

                return response;
            }
        } catch (Exception e) {
            log.error("获取酒店订单信息失败:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<GetHotelOrderResponse> getHotelOrderList(GetHotelOrderListRequest request) {
        List<GetHotelOrderResponse> list = hotelOrderMapper.getHotelOrderList(request);
        list.stream().forEach(x -> {
            try {
                if (StringUtils.isNoneBlank(x.getOrderTel())) {
                    x.setOrderTel(AESUtil.decrypt(x.getOrderTel(), aesKey, aesIv));
                }
            } catch (Exception e) {
                log.error("获取酒店订单信息列表敏感字段解密失败:{}", e.getMessage());
            }
        });
        return list;
    }

    @Override
    public void pushOrder() {
        // 获取待推送订单
        LambdaQueryWrapper<HotelOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotelOrder::getOrderStatus, 1);
        queryWrapper.eq(HotelOrder::getPushStatus, 1);
        List<HotelOrder> hotelOrderList = hotelOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(hotelOrderList)) {
            return;
        }

        // 推送订单
        hotelOrderList.stream().forEach(x -> {

            // todo

            x.setPushStatus(2);
            x.setPushResult("");
        });

        // 批量更新酒店预入住表推送状态
        hotelOrderMapper.updateBatchPush(hotelOrderList);
    }

    @Override
    public void sendSmsOrder() {
        // 查询预抵时间当天的订单
        LambdaQueryWrapper<HotelOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotelOrder::getOrderStatus, 1);
        queryWrapper.ge(HotelOrder::getArriveTime, DateUtils.getToday());
        queryWrapper.lt(HotelOrder::getArriveTime, DateUtils.getTomorrow());
        queryWrapper.eq(HotelOrder::getSmsStatus, 0);
        List<HotelOrder> hotelOrderList = hotelOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(hotelOrderList)) {
            return;
        }

        List<SmsRecord> smsRecordList = new ArrayList<>();
        // 发送短信提醒
        hotelOrderList.stream().forEach(x -> {

            // 发送短信
            smsService.sendSms();

            SmsRecord smsRecord = new SmsRecord();
            smsRecord.setMobile(x.getOrderTel());
            smsRecord.setSmsType(1);
            smsRecord.setSmsContent("尊敬的什么X先生，你今天即将入住...可以关注公众号，进入小程序后进行预入住，并支持在线选房...");
            smsRecord.setSmsStatus(2);
            smsRecord.setOutNo(x.getOrderNo());
            smsRecord.setCreateBy("system");
            smsRecord.setCreateTime(new Date());
            smsRecordList.add(smsRecord);

            x.setSmsStatus(2);
        });

        // 批量插入短信发送记录
        if (CollectionUtils.isNotEmpty(smsRecordList)) {
            smsRecordMapper.insertBatch(smsRecordList);
        }

        // 批量更新酒店订单表短信发送状态
        hotelOrderMapper.updateBatchSMS(hotelOrderList);

    }
}
