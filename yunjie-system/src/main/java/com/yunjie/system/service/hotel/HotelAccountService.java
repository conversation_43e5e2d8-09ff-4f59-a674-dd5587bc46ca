package com.yunjie.system.service.hotel;

import com.yunjie.system.model.request.hotel.account.*;
import com.yunjie.system.model.response.hotel.GetHotelAccountResponse;

import java.util.List;

public interface HotelAccountService {

    /**
     * 新增酒店账号信息
     * @param request
     * @return
     */
    int insertHotelAccount(InsertHotelAccountRequest request);

    /**
     * 更新酒店账号信息
     * @param request
     * @return
     */
    int updateHotelAccount(UpdateHotelAccountRequest request);

    /**
     * 变更酒店账号信息状态
     * @param request
     * @return
     */
    int changeStatus(ChangeHotelAccountStatusRequest request);

    /**
     * 删除酒店账号信息
     * @param request
     * @return
     */
    int deleteHotelAccount(DeleteHotelAccountRequest request);

    /**
     * 获取酒店账号信息
     * @param request
     * @return
     */
    GetHotelAccountResponse getHotelAccount(GetHotelAccountRequest request);

    /**
     * 获取酒店账号信息列表
     * @param request
     * @return
     */
    List<GetHotelAccountResponse> getHotelAccountList(GetHotelAccountListRequest request);
}
