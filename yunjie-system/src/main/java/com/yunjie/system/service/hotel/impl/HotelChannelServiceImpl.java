package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.utils.AESUtil;
import com.yunjie.common.utils.SecurityUtils;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelChannel;
import com.yunjie.system.mapper.hotel.HotelChannelMapper;
import com.yunjie.system.model.request.hotel.channel.*;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.response.hotel.GetHotelChannelResponse;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.service.hotel.HotelChannelService;
import com.yunjie.system.service.hotel.HotelInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelChannelServiceImpl implements HotelChannelService {

    @Resource
    private HotelChannelMapper hotelChannelMapper;

    @Resource
    private HotelInfoService hotelInfoService;

    @Value("${aes.key}")
    private String aesKey;

    @Value("${aes.iv}")
    private String aesIv;

    @Override
    public int insertHotelChannel(InsertHotelChannelRequest request) {
        try {
            HotelChannel hotelChannel = new HotelChannel();
            BeanUtils.copyBeanProp(hotelChannel, request);

            hotelChannel.setPassword(AESUtil.encrypt(request.getPassword(), aesKey, aesIv));
            hotelChannel.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            hotelChannel.setCreateTime(new Date());
            hotelChannel.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
            hotelChannel.setUpdateTime(new Date());
            return hotelChannelMapper.insert(hotelChannel);
        } catch (Exception e) {
            log.error("新增酒店渠道信息失败:{}", e.getMessage());
        }
        return 0;
    }

    @Override
    public int updateHotelChannel(UpdateHotelChannelRequest request) {
        try {
            LambdaUpdateWrapper<HotelChannel> updateWrapper = new LambdaUpdateWrapper<>();
            if (StringUtils.isNoneBlank(request.getAccountId())) {
                updateWrapper.set(HotelChannel::getAccountId, request.getAccountId());
            }
            if (StringUtils.isNoneBlank(request.getPassword())) {
                updateWrapper.set(HotelChannel::getPassword, AESUtil.encrypt(request.getPassword(), aesKey, aesIv));
            }
            updateWrapper.set(HotelChannel::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
            updateWrapper.set(HotelChannel::getUpdateTime, new Date());
            updateWrapper.eq(HotelChannel::getId, request.getId());
            return hotelChannelMapper.update(null, updateWrapper);
        } catch (Exception e) {
            log.error("更新酒店渠道信息失败:{}", e.getMessage());
        }
        return 0;
    }

    @Override
    public int changeStatus(ChangeHotelChannelStatusRequest request) {
        LambdaUpdateWrapper<HotelChannel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HotelChannel::getStatus, request.getStatus());
        updateWrapper.set(HotelChannel::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelChannel::getUpdateTime, new Date());
        updateWrapper.eq(HotelChannel::getId, request.getId());
        return hotelChannelMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteHotelChannel(DeleteHotelChannelRequest request) {
        request.setUserName(SecurityUtils.getLoginUser().getUsername());
        return hotelChannelMapper.deleteHotelChannelByIds(request);
    }

    @Override
    public GetHotelChannelResponse getHotelChannel(GetHotelChannelRequest request) {
        try {
            LambdaQueryWrapper<HotelChannel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotelChannel::getId, request.getId());
            queryWrapper.eq(HotelChannel::getDelFlag, 0);
            HotelChannel hotelChannel = hotelChannelMapper.selectOne(queryWrapper);
            if (hotelChannel != null) {
                GetHotelChannelResponse response = new GetHotelChannelResponse();
                BeanUtils.copyBeanProp(response, hotelChannel);

                // 获取酒店基本信息数据
                GetHotelInfoRequest getHotelInfoRequest = new GetHotelInfoRequest();
                getHotelInfoRequest.setId(hotelChannel.getHotelId());
                GetHotelInfoResponse getHotelInfoResponse = hotelInfoService.getHotelInfo(getHotelInfoRequest);
                if (getHotelInfoResponse != null) {
                    response.setHotelCode(getHotelInfoResponse.getHotelCode());
                    response.setHotelName(getHotelInfoResponse.getHotelName());
                }

                response.setPassword(AESUtil.decrypt(hotelChannel.getPassword(), aesKey, aesIv));
                return response;
            }
        } catch (Exception e) {
            log.error("获取酒店渠道信息失败:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<GetHotelChannelResponse> getHotelChannelList(GetHotelChannelListRequest request) {
        List<GetHotelChannelResponse> list = hotelChannelMapper.getHotelChannelList(request);
        list.forEach(x -> {
            try {
                x.setPassword(AESUtil.decrypt(x.getPassword(), aesKey, aesIv));
            } catch (Exception e) {
                log.error("获取酒店渠道信息列表敏感字段解密失败:{}", e.getMessage());
            }
        });
        return list;
    }
}
