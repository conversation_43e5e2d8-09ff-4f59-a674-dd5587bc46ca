package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.utils.AESUtil;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelCheckin;
import com.yunjie.system.domain.sms.SmsRecord;
import com.yunjie.system.mapper.hotel.HotelCheckinMapper;
import com.yunjie.system.mapper.sms.SmsRecordMapper;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinListRequest;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinRequest;
import com.yunjie.system.model.request.hotel.checkin.InsertHotelCheckinRequest;
import com.yunjie.system.model.request.hotel.checkin.UpdateHotelCheckinRequest;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.response.hotel.GetHotelCheckinResponse;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.service.hotel.HotelCheckinService;
import com.yunjie.system.service.hotel.HotelInfoService;
import com.yunjie.system.service.sms.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelCheckinServiceImpl implements HotelCheckinService {

    @Resource
    private HotelCheckinMapper hotelCheckinMapper;

    @Resource
    private SmsRecordMapper smsRecordMapper;

    @Resource
    private HotelInfoService hotelInfoService;

    @Resource
    private SmsService smsService;

    @Value("${aes.key}")
    private String aesKey;

    @Value("${aes.iv}")
    private String aesIv;

    @Override
    public R<GetHotelCheckinResponse> insertHotelCheckin(InsertHotelCheckinRequest request) {
        try {
            HotelCheckin hotelCheckin = new HotelCheckin();
            BeanUtils.copyBeanProp(hotelCheckin, request);

            if (StringUtils.isNoneBlank(request.getIdNumber())) {
                hotelCheckin.setIdNumber(AESUtil.encrypt(request.getIdNumber(), aesKey, aesIv));
            }

            if (StringUtils.isNoneBlank(request.getPhoneNumber())) {
                hotelCheckin.setPhoneNumber(AESUtil.encrypt(request.getPhoneNumber(), aesKey, aesIv));
            }

            hotelCheckin.setStatus(1);
            hotelCheckin.setCreateBy(request.getUserId().toString());
            hotelCheckin.setCreateTime(new Date());
            hotelCheckin.setUpdateBy(request.getUserId().toString());
            hotelCheckin.setUpdateTime(new Date());
            hotelCheckin.setPushStatus(1);
            hotelCheckin.setSmsStatus(0);
            int count = hotelCheckinMapper.insert(hotelCheckin);
            if (count > 0) {
                GetHotelCheckinResponse response = new GetHotelCheckinResponse();

                BeanUtils.copyBeanProp(response, hotelCheckin);

                if (StringUtils.isNoneBlank(hotelCheckin.getIdNumber())) {
                    response.setIdNumber(AESUtil.decrypt(hotelCheckin.getIdNumber(), aesKey, aesIv));
                }

                if (StringUtils.isNoneBlank(hotelCheckin.getPhoneNumber())) {
                    response.setPhoneNumber(AESUtil.decrypt(hotelCheckin.getPhoneNumber(), aesKey, aesIv));
                }

                return R.ok(response);
            }
        } catch (Exception e) {
            log.error("新增酒店预入住信息异常:{}", e.getMessage());
        }
        return R.fail("预入住失败");
    }

    @Override
    public R updateHotelCheckin(UpdateHotelCheckinRequest request) {
        LambdaUpdateWrapper<HotelCheckin> updateWrapper = new LambdaUpdateWrapper<>();
        if (request.getStatus() != null && request.getStatus() > 0) {
            updateWrapper.set(HotelCheckin::getStatus, request.getStatus());
        }
        updateWrapper.set(HotelCheckin::getUpdateBy, request.getGuestName());
        updateWrapper.set(HotelCheckin::getUpdateTime, new Date());
        updateWrapper.set(HotelCheckin::getSmsStatus, 0);
        updateWrapper.eq(HotelCheckin::getHotelCode, request.getHotelCode());
        updateWrapper.eq(HotelCheckin::getOrderNo, request.getOrderNo());
        int count = hotelCheckinMapper.update(null, updateWrapper);
        if (count > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @Override
    public GetHotelCheckinResponse getHotelCheckin(GetHotelCheckinRequest request) {
        try {
            LambdaQueryWrapper<HotelCheckin> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotelCheckin::getId, request.getId());
            if (request.getUserId() != null && request.getUserId() > 0) {
                queryWrapper.eq(HotelCheckin::getUserId, request.getUserId());
            }
            HotelCheckin hotelCheckin = hotelCheckinMapper.selectOne(queryWrapper);
            if (hotelCheckin != null) {
                GetHotelCheckinResponse response = new GetHotelCheckinResponse();
                BeanUtils.copyBeanProp(response, hotelCheckin);

                if (StringUtils.isNoneBlank(hotelCheckin.getIdNumber())) {
                    response.setIdNumber(AESUtil.decrypt(hotelCheckin.getIdNumber(), aesKey, aesIv));
                }

                if (StringUtils.isNoneBlank(hotelCheckin.getPhoneNumber())) {
                    response.setPhoneNumber(AESUtil.decrypt(hotelCheckin.getPhoneNumber(), aesKey, aesIv));
                }

                // 获取酒店基本信息数据
                GetHotelInfoRequest getHotelInfoRequest = new GetHotelInfoRequest();
                getHotelInfoRequest.setId(hotelCheckin.getHotelId());
                GetHotelInfoResponse getHotelInfoResponse = hotelInfoService.getHotelInfo(getHotelInfoRequest);
                if (getHotelInfoResponse != null) {
                    response.setHotelName(getHotelInfoResponse.getHotelName());
                }

                return response;
            }
        } catch (Exception e) {
            log.error("获取酒店预入住信息失败:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<GetHotelCheckinResponse> getHotelCheckinList(GetHotelCheckinListRequest request) {
        List<GetHotelCheckinResponse> list = hotelCheckinMapper.getHotelCheckinList(request);
        list.forEach(x -> {
            try {
                if (StringUtils.isNoneBlank(x.getIdNumber())) {
                    x.setIdNumber(AESUtil.decrypt(x.getIdNumber(), aesKey, aesIv));
                }

                if (StringUtils.isNoneBlank(x.getPhoneNumber())) {
                    x.setPhoneNumber(AESUtil.decrypt(x.getPhoneNumber(), aesKey, aesIv));
                }
            } catch (Exception e) {
                log.error("获取酒店预入住信息列表敏感字段解密失败:{}", e.getMessage());
            }
        });
        return list;
    }

    @Override
    public void pushCheckin() {
        // 获取待推送预入住信息
        LambdaQueryWrapper<HotelCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotelCheckin::getStatus, 1);
        queryWrapper.eq(HotelCheckin::getPushStatus, 1);
        List<HotelCheckin> hotelCheckinList = hotelCheckinMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(hotelCheckinList)) {
            return;
        }

        // 推送预入住信息
        hotelCheckinList.stream().forEach(x -> {

            // todo

            x.setStatus(2);
            x.setPushStatus(2);
            x.setPushResult("");
        });

        // 批量更新酒店预入住表推送状态
        hotelCheckinMapper.updateBatchPush(hotelCheckinList);
    }

    @Override
    public void sendSmsCheckin() {
        // 获取预订单在第一个人入住后超过1小时还有人员还未入住的人员信息
        List<GetHotelCheckinResponse> checkinList = hotelCheckinMapper.getNoShowCheckinList();
        if (CollectionUtils.isEmpty(checkinList)) {
            return;
        }

        List<SmsRecord> smsRecordList = new ArrayList<>();
        // 短信提醒
        checkinList.stream().forEach(x -> {

            // 发送短信
            smsService.sendSms();

            SmsRecord smsRecord = new SmsRecord();
            smsRecord.setMobile(x.getPhoneNumber());
            smsRecord.setSmsType(2);
            smsRecord.setSmsContent("尊敬的什么X先生，你今天即将入住...可以关注公众号，进入小程序后进行预入住，并支持在线选房...");
            smsRecord.setSmsStatus(2);
            smsRecord.setOutNo(x.getOrderNo());
            smsRecord.setCreateBy("system");
            smsRecord.setCreateTime(new Date());
            smsRecordList.add(smsRecord);

            x.setSmsStatus(2);
        });

        // 批量插入短信发送记录
        if (CollectionUtils.isNotEmpty(smsRecordList)) {
            smsRecordMapper.insertBatch(smsRecordList);
        }

        // 批量更新酒店预入住表短信发送状态
        hotelCheckinMapper.updateBatchSMS(checkinList);
    }
}
