package com.yunjie.system.service.wechat;

import com.yunjie.common.core.domain.R;
import com.yunjie.system.model.request.wechat.AuthPhoneRequest;
import com.yunjie.system.model.request.wechat.GetUserPhoneRequest;
import com.yunjie.system.model.request.wechat.GetAccessTokenRequest;
import com.yunjie.system.model.request.wechat.WechatLoginRequest;
import com.yunjie.system.model.response.hotel.HotelUserResponse;
import com.yunjie.system.model.response.wechat.GetUserPhoneResponse;
import com.yunjie.system.model.response.wechat.GetAccessTokenResponse;

public interface WechatService {

    /**
     * 微信小程序登录
     * @param request
     * @return
     */
    R<HotelUserResponse> wechatLogin(WechatLoginRequest request);

    /**
     * 获取手机号
     * @param request
     * @return
     */
    R<GetUserPhoneResponse> getUserPhone(GetUserPhoneRequest request);

    /**
     * 授权手机号注册
     * @param request
     * @return
     */
    R<HotelUserResponse> authPhoneRegister(AuthPhoneRequest request);

    /**
     * 获取access_token
     * @return
     */
    R<GetAccessTokenResponse> getAccessToken(GetAccessTokenRequest request);
}
