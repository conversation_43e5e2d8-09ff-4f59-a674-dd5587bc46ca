package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.utils.SecurityUtils;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelInfo;
import com.yunjie.system.mapper.hotel.HotelInfoMapper;
import com.yunjie.system.model.request.hotel.info.*;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.service.hotel.HotelInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelInfoServiceImpl implements HotelInfoService {

    @Resource
    private HotelInfoMapper hotelInfoMapper;

    @Override
    public int insertHotelInfo(InsertHotelInfoRequest request) {
        HotelInfo hotelInfo = new HotelInfo();
        BeanUtils.copyBeanProp(hotelInfo, request);
        hotelInfo.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        hotelInfo.setCreateTime(new Date());
        hotelInfo.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        hotelInfo.setUpdateTime(new Date());
        return hotelInfoMapper.insert(hotelInfo);
    }

    @Override
    public int updateHotelInfo(UpdateHotelInfoRequest request) {
        LambdaUpdateWrapper<HotelInfo> updateWrapper = new LambdaUpdateWrapper<>();
        if (StringUtils.isNoneBlank(request.getHotelCode())) {
            updateWrapper.set(HotelInfo::getHotelCode, request.getHotelCode());
        }
        if (StringUtils.isNoneBlank(request.getHotelName())) {
            updateWrapper.set(HotelInfo::getHotelName, request.getHotelName());
        }
        if (request.getHotelType() != null && request.getHotelType() > 0) {
            updateWrapper.set(HotelInfo::getHotelType, request.getHotelType());
        }
        if (request.getStarLevel() != null && request.getStarLevel() > 0) {
            updateWrapper.set(HotelInfo::getStarLevel, request.getStarLevel());
        }
        if (StringUtils.isNoneBlank(request.getHotelPic())) {
            updateWrapper.set(HotelInfo::getHotelPic, request.getHotelPic());
        }
        if (StringUtils.isNoneBlank(request.getProvinceCode())) {
            updateWrapper.set(HotelInfo::getProvinceCode, request.getProvinceCode());
        }
        if (StringUtils.isNoneBlank(request.getCityCode())) {
            updateWrapper.set(HotelInfo::getCityCode, request.getCityCode());
        }
        if (StringUtils.isNoneBlank(request.getDistrictCode())) {
            updateWrapper.set(HotelInfo::getDistrictCode, request.getDistrictCode());
        }
        if (StringUtils.isNoneBlank(request.getDetailAddress())) {
            updateWrapper.set(HotelInfo::getDetailAddress, request.getDetailAddress());
        }
        if (request.getLongitude() != null && request.getLongitude().compareTo(BigDecimal.ZERO) > 0) {
            updateWrapper.set(HotelInfo::getLongitude, request.getLongitude());
        }
        if (request.getLatitude() != null && request.getLatitude().compareTo(BigDecimal.ZERO) > 0) {
            updateWrapper.set(HotelInfo::getLatitude, request.getLatitude());
        }
        if (StringUtils.isNoneBlank(request.getTelephone())) {
            updateWrapper.set(HotelInfo::getTelephone, request.getTelephone());
        }
        updateWrapper.set(HotelInfo::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelInfo::getUpdateTime, new Date());
        updateWrapper.eq(HotelInfo::getId, request.getId());
        return hotelInfoMapper.update(null, updateWrapper);
    }

    @Override
    public int changeStatus(ChangeHotelInfoStatusRequest request) {
        LambdaUpdateWrapper<HotelInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HotelInfo::getStatus, request.getStatus());
        updateWrapper.set(HotelInfo::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelInfo::getUpdateTime, new Date());
        updateWrapper.eq(HotelInfo::getId, request.getId());
        return hotelInfoMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteHotelInfo(DeleteHotelInfoRequest request) {
        request.setUserName(SecurityUtils.getLoginUser().getUsername());
        return hotelInfoMapper.deleteHotelInfoByIds(request);
    }

    @Override
    public GetHotelInfoResponse getHotelInfo(GetHotelInfoRequest request) {
        return hotelInfoMapper.getHotelInfo(request);
    }

    @Override
    public List<GetHotelInfoResponse> getHotelInfoList(GetHotelInfoListRequest request) {
        return hotelInfoMapper.getHotelInfoList(request);
    }

    @Override
    public List<GetHotelInfoResponse> getNearHotelList(GetNearHotelListRequest request) {
        return hotelInfoMapper.getNearHotelList(request);
    }
}
