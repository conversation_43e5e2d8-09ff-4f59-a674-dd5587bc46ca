package com.yunjie.system.service.wechat.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.wechat.WechatBaseInfo;
import com.yunjie.system.mapper.wechat.WechatBaseInfoMapper;
import com.yunjie.system.model.request.wechat.GetWechatBaseInfoRequest;
import com.yunjie.system.model.response.wechat.GetWechatBaseInfoResponse;
import com.yunjie.system.service.wechat.WechatBaseInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WechatBaseInfoServiceImpl implements WechatBaseInfoService {

    @Resource
    private WechatBaseInfoMapper wechatBaseInfoMapper;

    @Override
    public GetWechatBaseInfoResponse getWechatBaseInfo(GetWechatBaseInfoRequest request) {
        LambdaQueryWrapper<WechatBaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WechatBaseInfo::getAppId, request.getAppId());
        queryWrapper.eq(WechatBaseInfo::getAppStatus, 1);
        WechatBaseInfo wechatBaseInfo = wechatBaseInfoMapper.selectOne(queryWrapper);
        if (wechatBaseInfo != null) {
            GetWechatBaseInfoResponse response = new GetWechatBaseInfoResponse();
            BeanUtils.copyBeanProp(response, wechatBaseInfo);
            return response;
        }
        return null;
    }
}
