package com.yunjie.system.service.hotel;

import com.yunjie.system.model.request.hotel.knowledge.*;
import com.yunjie.system.model.response.hotel.GetHotelKnowledgeResponse;

import java.util.List;

public interface HotelKnowledgeService {

    /**
     * 新增酒店知识库信息
     * @param request
     * @return
     */
    int insertHotelKnowledge(InsertHotelKnowledgeRequest request);

    /**
     * 更新酒店知识库信息
     * @param request
     * @return
     */
    int updateHotelKnowledge(UpdateHotelKnowledgeRequest request);

    /**
     * 变更酒店知识库信息状态
     * @param request
     * @return
     */
    int changeStatus(ChangeHotelKnowledgeStatusRequest request);

    /**
     * 删除酒店知识库信息
     * @param request
     * @return
     */
    int deleteHotelKnowledge(DeleteHotelKnowledgeRequest request);

    /**
     * 获取酒店知识库信息
     * @param request
     * @return
     */
    GetHotelKnowledgeResponse getHotelKnowledge(GetHotelKnowledgeRequest request);

    /**
     * 获取酒店知识库信息列表
     * @param request
     * @return
     */
    List<GetHotelKnowledgeResponse> getHotelKnowledgeList(GetHotelKnowledgeListRequest request);
}
