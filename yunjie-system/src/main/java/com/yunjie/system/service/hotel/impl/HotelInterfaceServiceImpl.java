package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.utils.SecurityUtils;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelInterface;
import com.yunjie.system.mapper.hotel.HotelInterfaceMapper;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.request.hotel.interfaces.*;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.model.response.hotel.GetHotelInterfaceResponse;
import com.yunjie.system.service.hotel.HotelInfoService;
import com.yunjie.system.service.hotel.HotelInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelInterfaceServiceImpl implements HotelInterfaceService {

    @Resource
    private HotelInterfaceMapper hotelInterfaceMapper;

    @Resource
    private HotelInfoService hotelInfoService;

    @Override
    public int insertHotelInterface(InsertHotelInterfaceRequest request) {
        HotelInterface hotelInterface = new HotelInterface();
        BeanUtils.copyBeanProp(hotelInterface, request);
        hotelInterface.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        hotelInterface.setCreateTime(new Date());
        hotelInterface.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        hotelInterface.setUpdateTime(new Date());
        return hotelInterfaceMapper.insert(hotelInterface);
    }

    @Override
    public int updateHotelInterface(UpdateHotelInterfaceRequest request) {
        LambdaUpdateWrapper<HotelInterface> updateWrapper = new LambdaUpdateWrapper<>();
        if (StringUtils.isNoneBlank(request.getDomainUrl())) {
            updateWrapper.set(HotelInterface::getDomainUrl, request.getDomainUrl());
        }
        if (StringUtils.isNoneBlank(request.getInterfaceUrl())) {
            updateWrapper.set(HotelInterface::getInterfaceUrl, request.getInterfaceUrl());
        }
        if (StringUtils.isNoneBlank(request.getInterfaceDesc())) {
            updateWrapper.set(HotelInterface::getInterfaceDesc, request.getInterfaceDesc());
        }
        updateWrapper.set(HotelInterface::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelInterface::getUpdateTime, new Date());
        updateWrapper.eq(HotelInterface::getId, request.getId());
        return hotelInterfaceMapper.update(null, updateWrapper);
    }

    @Override
    public int changeStatus(ChangeHotelInterfaceStatusRequest request) {
        LambdaUpdateWrapper<HotelInterface> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HotelInterface::getStatus, request.getStatus());
        updateWrapper.set(HotelInterface::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelInterface::getUpdateTime, new Date());
        updateWrapper.eq(HotelInterface::getId, request.getId());
        return hotelInterfaceMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteHotelInterface(DeleteHotelInterfaceRequest request) {
        request.setUserName(SecurityUtils.getLoginUser().getUsername());
        return hotelInterfaceMapper.deleteHotelInterfaceByIds(request);
    }

    @Override
    public GetHotelInterfaceResponse getHotelInterface(GetHotelInterfaceRequest request) {
        LambdaQueryWrapper<HotelInterface> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotelInterface::getId, request.getId());
        queryWrapper.eq(HotelInterface::getDelFlag, 0);
        HotelInterface hotelInterface = hotelInterfaceMapper.selectOne(queryWrapper);
        if (hotelInterface != null) {
            GetHotelInterfaceResponse response = new GetHotelInterfaceResponse();
            BeanUtils.copyBeanProp(response, hotelInterface);

            // 获取酒店基本信息数据
            GetHotelInfoRequest getHotelInfoRequest = new GetHotelInfoRequest();
            getHotelInfoRequest.setId(hotelInterface.getHotelId());
            GetHotelInfoResponse getHotelInfoResponse = hotelInfoService.getHotelInfo(getHotelInfoRequest);
            if (getHotelInfoResponse != null) {
                response.setHotelCode(getHotelInfoResponse.getHotelCode());
                response.setHotelName(getHotelInfoResponse.getHotelName());
            }
            return response;
        }
        return null;
    }

    @Override
    public List<GetHotelInterfaceResponse> getHotelInterfaceList(GetHotelInterfaceListRequest request) {
        return hotelInterfaceMapper.getHotelInterfaceList(request);
    }
}
