package com.yunjie.system.service.hotel;

import com.yunjie.common.core.domain.R;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderListRequest;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderRequest;
import com.yunjie.system.model.request.hotel.order.InsertHotelOrderRequest;
import com.yunjie.system.model.request.hotel.order.UpdateHotelOrderRequest;
import com.yunjie.system.model.response.hotel.GetHotelOrderResponse;

import java.util.List;

public interface HotelOrderService {

    /**
     * 新增酒店订单信息
     * @param request
     * @return
     */
    R insertHotelOrder(InsertHotelOrderRequest request);

    /**
     * 更新酒店订单信息
     * @param request
     * @return
     */
    R updateHotelOrder(UpdateHotelOrderRequest request);

    /**
     * 获取酒店订单信息
     * @param request
     * @return
     */
    GetHotelOrderResponse getHotelOrder(GetHotelOrderRequest request);

    /**
     * 获取酒店订单信息列表
     * @param request
     * @return
     */
    List<GetHotelOrderResponse> getHotelOrderList(GetHotelOrderListRequest request);

    /**
     * 推送订单信息
     * @return
     */
    void pushOrder();

    /**
     * 预订单短信提醒
     * @return
     */
    void sendSmsOrder();
}
