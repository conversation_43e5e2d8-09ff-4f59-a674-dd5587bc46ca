package com.yunjie.system.service.hotel;

import com.yunjie.system.model.request.hotel.interfaces.*;
import com.yunjie.system.model.response.hotel.GetHotelInterfaceResponse;

import java.util.List;

public interface HotelInterfaceService {

    /**
     * 新增酒店PMS对接地址信息
     * @param request
     * @return
     */
    int insertHotelInterface(InsertHotelInterfaceRequest request);

    /**
     * 更新酒店PMS对接地址信息
     * @param request
     * @return
     */
    int updateHotelInterface(UpdateHotelInterfaceRequest request);

    /**
     * 变更酒店PMS对接地址信息状态
     * @param request
     * @return
     */
    int changeStatus(ChangeHotelInterfaceStatusRequest request);

    /**
     * 删除酒店PMS对接地址信息
     * @param request
     * @return
     */
    int deleteHotelInterface(DeleteHotelInterfaceRequest request);

    /**
     * 获取酒店PMS对接地址信息
     * @param request
     * @return
     */
    GetHotelInterfaceResponse getHotelInterface(GetHotelInterfaceRequest request);

    /**
     * 获取酒店PMS对接地址信息列表
     * @param request
     * @return
     */
    List<GetHotelInterfaceResponse> getHotelInterfaceList(GetHotelInterfaceListRequest request);
}
