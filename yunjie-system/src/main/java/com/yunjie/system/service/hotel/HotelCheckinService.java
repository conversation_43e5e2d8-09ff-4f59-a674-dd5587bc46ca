package com.yunjie.system.service.hotel;

import com.yunjie.common.core.domain.R;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinListRequest;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinRequest;
import com.yunjie.system.model.request.hotel.checkin.InsertHotelCheckinRequest;
import com.yunjie.system.model.request.hotel.checkin.UpdateHotelCheckinRequest;
import com.yunjie.system.model.response.hotel.GetHotelCheckinResponse;

import java.util.List;

public interface HotelCheckinService {

    /**
     * 新增酒店预入住信息
     * @param request
     * @return
     */
    R<GetHotelCheckinResponse> insertHotelCheckin(InsertHotelCheckinRequest request);

    /**
     * 更新酒店预入住信息
     * @param request
     * @return
     */
    R updateHotelCheckin(UpdateHotelCheckinRequest request);

    /**
     * 获取酒店预入住信息
     * @param request
     * @return
     */
    GetHotelCheckinResponse getHotelCheckin(GetHotelCheckinRequest request);

    /**
     * 获取酒店预入住信息列表
     * @param request
     * @return
     */
    List<GetHotelCheckinResponse> getHotelCheckinList(GetHotelCheckinListRequest request);

    /**
     * 推送预入住信息
     * @return
     */
    void pushCheckin();

    /**
     * 预入住短信提醒
     * @return
     */
    void sendSmsCheckin();
}
