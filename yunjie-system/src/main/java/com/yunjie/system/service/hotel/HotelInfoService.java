package com.yunjie.system.service.hotel;

import com.yunjie.system.model.request.hotel.info.*;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;

import java.util.List;

public interface HotelInfoService {

    /**
     * 新增酒店基本信息
     * @param request
     * @return
     */
    int insertHotelInfo(InsertHotelInfoRequest request);

    /**
     * 更新酒店基本信息
     * @param request
     * @return
     */
    int updateHotelInfo(UpdateHotelInfoRequest request);

    /**
     * 变更酒店基本信息状态
     * @param request
     * @return
     */
    int changeStatus(ChangeHotelInfoStatusRequest request);

    /**
     * 删除酒店基本信息
     * @param request
     * @return
     */
    int deleteHotelInfo(DeleteHotelInfoRequest request);

    /**
     * 获取酒店基本信息
     * @param request
     * @return
     */
    GetHotelInfoResponse getHotelInfo(GetHotelInfoRequest request);

    /**
     * 获取酒店基本信息列表
     * @param request
     * @return
     */
    List<GetHotelInfoResponse> getHotelInfoList(GetHotelInfoListRequest request);

    /**
     * 获取附近酒店列表
     * @param request
     * @return
     */
    List<GetHotelInfoResponse> getNearHotelList(GetNearHotelListRequest request);
}
