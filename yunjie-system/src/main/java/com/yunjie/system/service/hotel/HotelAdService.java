package com.yunjie.system.service.hotel;

import com.yunjie.system.model.request.hotel.ad.*;
import com.yunjie.system.model.response.hotel.GetHotelAdResponse;

import java.util.List;

public interface HotelAdService {

    /**
     * 新增酒店广告信息
     * @param request
     * @return
     */
    int insertHotelAd(InsertHotelAdRequest request);

    /**
     * 更新酒店广告信息
     * @param request
     * @return
     */
    int updateHotelAd(UpdateHotelAdRequest request);

    /**
     * 变更酒店广告信息状态
     * @param request
     * @return
     */
    int changeStatus(ChangeHotelAdStatusRequest request);

    /**
     * 删除酒店广告信息
     * @param request
     * @return
     */
    int deleteHotelAd(DeleteHotelAdRequest request);

    /**
     * 获取酒店广告信息
     * @param request
     * @return
     */
    GetHotelAdResponse getHotelAd(GetHotelAdRequest request);

    /**
     * 获取酒店广告信息列表
     * @param request
     * @return
     */
    List<GetHotelAdResponse> getHotelAdList(GetHotelAdListRequest request);
}
