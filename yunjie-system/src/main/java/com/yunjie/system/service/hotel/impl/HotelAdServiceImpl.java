package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.utils.SecurityUtils;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelAd;
import com.yunjie.system.mapper.hotel.HotelAdMapper;
import com.yunjie.system.model.request.hotel.ad.*;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.response.hotel.GetHotelAdResponse;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.service.hotel.HotelAdService;
import com.yunjie.system.service.hotel.HotelInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelAdServiceImpl implements HotelAdService {

    @Resource
    private HotelAdMapper hotelAdMapper;

    @Resource
    private HotelInfoService hotelInfoService;

    @Override
    public int insertHotelAd(InsertHotelAdRequest request) {
        HotelAd hotelAd = new HotelAd();
        BeanUtils.copyBeanProp(hotelAd, request);
        hotelAd.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        hotelAd.setCreateTime(new Date());
        hotelAd.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        hotelAd.setUpdateTime(new Date());
        return hotelAdMapper.insert(hotelAd);
    }

    @Override
    public int updateHotelAd(UpdateHotelAdRequest request) {
        LambdaUpdateWrapper<HotelAd> updateWrapper = new LambdaUpdateWrapper<>();
        if (StringUtils.isNoneBlank(request.getAdName())) {
            updateWrapper.set(HotelAd::getAdName, request.getAdName());
        }
        if (StringUtils.isNoneBlank(request.getAdDesc())) {
            updateWrapper.set(HotelAd::getAdDesc, request.getAdDesc());
        }
        if (StringUtils.isNoneBlank(request.getAdUrl())) {
            updateWrapper.set(HotelAd::getAdUrl, request.getAdUrl());
        }
        updateWrapper.set(HotelAd::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelAd::getUpdateTime, new Date());
        updateWrapper.eq(HotelAd::getId, request.getId());
        return hotelAdMapper.update(null, updateWrapper);
    }

    @Override
    public int changeStatus(ChangeHotelAdStatusRequest request) {
        LambdaUpdateWrapper<HotelAd> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HotelAd::getStatus, request.getStatus());
        updateWrapper.set(HotelAd::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelAd::getUpdateTime, new Date());
        updateWrapper.eq(HotelAd::getId, request.getId());
        return hotelAdMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteHotelAd(DeleteHotelAdRequest request) {
        request.setUserName(SecurityUtils.getLoginUser().getUsername());
        return hotelAdMapper.deleteHotelAdByIds(request);
    }

    @Override
    public GetHotelAdResponse getHotelAd(GetHotelAdRequest request) {
        LambdaQueryWrapper<HotelAd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotelAd::getId, request.getId());
        queryWrapper.eq(HotelAd::getDelFlag, 0);
        HotelAd hotelAd = hotelAdMapper.selectOne(queryWrapper);
        if (hotelAd != null) {
            GetHotelAdResponse response = new GetHotelAdResponse();
            BeanUtils.copyBeanProp(response, hotelAd);

            // 获取酒店基本信息数据
            GetHotelInfoRequest getHotelInfoRequest = new GetHotelInfoRequest();
            getHotelInfoRequest.setId(hotelAd.getHotelId());
            GetHotelInfoResponse getHotelInfoResponse = hotelInfoService.getHotelInfo(getHotelInfoRequest);
            if (getHotelInfoResponse != null) {
                response.setHotelCode(getHotelInfoResponse.getHotelCode());
                response.setHotelName(getHotelInfoResponse.getHotelName());
            }
            return response;
        }
        return null;
    }

    @Override
    public List<GetHotelAdResponse> getHotelAdList(GetHotelAdListRequest request) {
        return hotelAdMapper.getHotelAdList(request);
    }
}
