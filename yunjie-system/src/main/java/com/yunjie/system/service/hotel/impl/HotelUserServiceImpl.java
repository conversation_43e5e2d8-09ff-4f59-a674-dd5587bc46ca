package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.utils.AESUtil;
import com.yunjie.common.utils.SecurityUtils;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelUser;
import com.yunjie.system.mapper.hotel.HotelUserMapper;
import com.yunjie.system.model.request.hotel.user.*;
import com.yunjie.system.model.response.hotel.GetHotelUserResponse;
import com.yunjie.system.service.hotel.HotelUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelUserServiceImpl implements HotelUserService {

    @Resource
    private HotelUserMapper hotelUserMapper;

    @Value("${aes.key}")
    private String aesKey;

    @Value("${aes.iv}")
    private String aesIv;

    @Override
    public R<GetHotelUserResponse> insertHotelUser(InsertHotelUserRequest request) {
        try {
            HotelUser hotelUser = new HotelUser();
            BeanUtils.copyBeanProp(hotelUser, request);

            if (StringUtils.isNoneBlank(request.getMobile())) {
                hotelUser.setMobile(AESUtil.encrypt(request.getMobile(), aesKey, aesIv));
                hotelUser.setBindFlag(1);
            }

            hotelUser.setStatus(0);
            hotelUser.setCreateBy(request.getOpenid());
            hotelUser.setCreateTime(new Date());
            hotelUser.setUpdateBy(request.getOpenid());
            hotelUser.setUpdateTime(new Date());
            int count = hotelUserMapper.insert(hotelUser);
            if (count > 0) {
                GetHotelUserResponse response = new GetHotelUserResponse();
                BeanUtils.copyBeanProp(response, hotelUser);

                if (StringUtils.isNoneBlank(hotelUser.getMobile())) {
                    response.setMobile(AESUtil.decrypt(hotelUser.getMobile(), aesKey, aesIv));
                }

                return R.ok(response);
            }
        } catch (Exception e) {
            log.error("新增酒店用户信息异常:{}", e.getMessage());
        }
        return R.fail("注册失败");
    }

    @Override
    public int updateHotelUser(UpdateHotelUserRequest request) {
        try {
            LambdaUpdateWrapper<HotelUser> updateWrapper = new LambdaUpdateWrapper<>();
            if (StringUtils.isNoneBlank(request.getNickName())) {
                updateWrapper.set(HotelUser::getNickName, request.getNickName());
            }
            if (StringUtils.isNoneBlank(request.getAvatarUrl())) {
                updateWrapper.set(HotelUser::getAvatarUrl, request.getAvatarUrl());
            }
            if (StringUtils.isNoneBlank(request.getUnionId())) {
                updateWrapper.set(HotelUser::getUnionId, request.getUnionId());
            }
            if (StringUtils.isNoneBlank(request.getMobile())) {
                updateWrapper.set(HotelUser::getMobile, AESUtil.encrypt(request.getMobile(), aesKey, aesIv));
                updateWrapper.set(HotelUser::getBindFlag, 1);
            }
            updateWrapper.set(HotelUser::getUpdateBy, request.getUserId());
            updateWrapper.set(HotelUser::getUpdateTime, new Date());
            updateWrapper.eq(HotelUser::getUserId, request.getUserId());
            return hotelUserMapper.update(null, updateWrapper);
        } catch (Exception e) {
            log.error("更新酒店用户信息失败:{}", e.getMessage());
        }
        return 0;
    }

    @Override
    public int changeStatus(ChangeHotelUserStatusRequest request) {
        LambdaUpdateWrapper<HotelUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HotelUser::getStatus, request.getStatus());
        updateWrapper.set(HotelUser::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelUser::getUpdateTime, new Date());
        updateWrapper.eq(HotelUser::getUserId, request.getUserId());
        return hotelUserMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteHotelUser(DeleteHotelUserRequest request) {
        request.setUserName(SecurityUtils.getLoginUser().getUsername());
        return hotelUserMapper.deleteHotelUserByIds(request);
    }

    @Override
    public GetHotelUserResponse getHotelUser(GetHotelUserRequest request) {
        try {
            LambdaQueryWrapper<HotelUser> queryWrapper = new LambdaQueryWrapper<>();
            if (request.getUserId() != null && request.getUserId() > 0) {
                queryWrapper.eq(HotelUser::getUserId, request.getUserId());
            }
            if (StringUtils.isNoneBlank(request.getOpenid())) {
                queryWrapper.eq(HotelUser::getOpenid, request.getOpenid());
            }
            if (StringUtils.isNoneBlank(request.getUnionId())) {
                queryWrapper.eq(HotelUser::getUnionId, request.getUnionId());
            }
            if (StringUtils.isNoneBlank(request.getMobile())) {
                queryWrapper.eq(HotelUser::getMobile, AESUtil.encrypt(request.getMobile(), aesKey, aesIv));
            }
            if (StringUtils.isNoneBlank(request.getAppId())) {
                queryWrapper.eq(HotelUser::getAppId, request.getAppId());
            }
            queryWrapper.eq(HotelUser::getDelFlag, 0);
            HotelUser hotelUser = hotelUserMapper.selectOne(queryWrapper);
            if (hotelUser != null) {
                GetHotelUserResponse response = new GetHotelUserResponse();
                BeanUtils.copyBeanProp(response, hotelUser);

                if (StringUtils.isNoneBlank(hotelUser.getMobile())) {
                    response.setMobile(AESUtil.decrypt(hotelUser.getMobile(), aesKey, aesIv));
                }
                return response;
            }
        } catch (Exception e) {
            log.error("获取酒店用户信息失败:{}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<GetHotelUserResponse> getHotelUserList(GetHotelUserListRequest request) {
        List<GetHotelUserResponse> list = hotelUserMapper.getHotelUserList(request);
        list.forEach(x -> {
            try {
                if (StringUtils.isNoneBlank(x.getMobile())) {
                    x.setMobile(AESUtil.decrypt(x.getMobile(), aesKey, aesIv));
                }
            } catch (Exception e) {
                log.error("获取酒店用户信息列表敏感字段解密失败:{}", e.getMessage());
            }
        });
        return list;
    }
}
