package com.yunjie.system.service.hotel;

import com.yunjie.common.core.domain.R;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingListRequest;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingRequest;
import com.yunjie.system.model.request.hotel.booking.InsertHotelBookingRequest;
import com.yunjie.system.model.response.hotel.GetHotelBookingResponse;

import java.util.List;

public interface HotelBookingService {

    /**
     * 新增酒店预订信息
     * @param request
     * @return
     */
    R<GetHotelBookingResponse> insertHotelBooking(InsertHotelBookingRequest request);

    /**
     * 获取酒店预订信息
     * @param request
     * @return
     */
    GetHotelBookingResponse getHotelBooking(GetHotelBookingRequest request);

    /**
     * 获取酒店预订信息列表
     * @param request
     * @return
     */
    List<GetHotelBookingResponse> getHotelBookingList(GetHotelBookingListRequest request);
}
