package com.yunjie.system.service.hotel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunjie.common.utils.SecurityUtils;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.domain.hotel.HotelKnowledge;
import com.yunjie.system.mapper.hotel.HotelKnowledgeMapper;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.request.hotel.knowledge.*;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.model.response.hotel.GetHotelKnowledgeResponse;
import com.yunjie.system.service.hotel.HotelInfoService;
import com.yunjie.system.service.hotel.HotelKnowledgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class HotelKnowledgeServiceImpl implements HotelKnowledgeService {

    @Resource
    private HotelKnowledgeMapper hotelKnowledgeMapper;

    @Resource
    private HotelInfoService hotelInfoService;

    @Override
    public int insertHotelKnowledge(InsertHotelKnowledgeRequest request) {
        HotelKnowledge hotelKnowledge = new HotelKnowledge();
        BeanUtils.copyBeanProp(hotelKnowledge, request);
        hotelKnowledge.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        hotelKnowledge.setCreateTime(new Date());
        hotelKnowledge.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        hotelKnowledge.setUpdateTime(new Date());
        return hotelKnowledgeMapper.insert(hotelKnowledge);
    }

    @Override
    public int updateHotelKnowledge(UpdateHotelKnowledgeRequest request) {
        LambdaUpdateWrapper<HotelKnowledge> updateWrapper = new LambdaUpdateWrapper<>();
        if (StringUtils.isNoneBlank(request.getKnowledgeName())) {
            updateWrapper.set(HotelKnowledge::getKnowledgeName, request.getKnowledgeName());
        }
        if (StringUtils.isNoneBlank(request.getKnowledgeDesc())) {
            updateWrapper.set(HotelKnowledge::getKnowledgeDesc, request.getKnowledgeDesc());
        }
        if (request.getKnowledgeType() != null && request.getKnowledgeType() > 0) {
            updateWrapper.set(HotelKnowledge::getKnowledgeType, request.getKnowledgeType());
        }
        if (StringUtils.isNoneBlank(request.getKnowledgeContent())) {
            updateWrapper.set(HotelKnowledge::getKnowledgeContent, request.getKnowledgeContent());
        }
        updateWrapper.set(HotelKnowledge::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelKnowledge::getUpdateTime, new Date());
        updateWrapper.eq(HotelKnowledge::getId, request.getId());
        return hotelKnowledgeMapper.update(null, updateWrapper);
    }

    @Override
    public int changeStatus(ChangeHotelKnowledgeStatusRequest request) {
        LambdaUpdateWrapper<HotelKnowledge> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(HotelKnowledge::getStatus, request.getStatus());
        updateWrapper.set(HotelKnowledge::getUpdateBy, SecurityUtils.getLoginUser().getUsername());
        updateWrapper.set(HotelKnowledge::getUpdateTime, new Date());
        updateWrapper.eq(HotelKnowledge::getId, request.getId());
        return hotelKnowledgeMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteHotelKnowledge(DeleteHotelKnowledgeRequest request) {
        request.setUserName(SecurityUtils.getLoginUser().getUsername());
        return hotelKnowledgeMapper.deleteHotelKnowledgeByIds(request);
    }

    @Override
    public GetHotelKnowledgeResponse getHotelKnowledge(GetHotelKnowledgeRequest request) {
        LambdaQueryWrapper<HotelKnowledge> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotelKnowledge::getId, request.getId());
        queryWrapper.eq(HotelKnowledge::getDelFlag, 0);
        HotelKnowledge hotelKnowledge = hotelKnowledgeMapper.selectOne(queryWrapper);
        if (hotelKnowledge != null) {
            GetHotelKnowledgeResponse response = new GetHotelKnowledgeResponse();
            BeanUtils.copyBeanProp(response, hotelKnowledge);

            // 获取酒店基本信息数据
            GetHotelInfoRequest getHotelInfoRequest = new GetHotelInfoRequest();
            getHotelInfoRequest.setId(hotelKnowledge.getHotelId());
            GetHotelInfoResponse getHotelInfoResponse = hotelInfoService.getHotelInfo(getHotelInfoRequest);
            if (getHotelInfoResponse != null) {
                response.setHotelCode(getHotelInfoResponse.getHotelCode());
                response.setHotelName(getHotelInfoResponse.getHotelName());
            }
            return response;
        }
        return null;
    }

    @Override
    public List<GetHotelKnowledgeResponse> getHotelKnowledgeList(GetHotelKnowledgeListRequest request) {
        return hotelKnowledgeMapper.getHotelKnowledgeList(request);
    }
}
