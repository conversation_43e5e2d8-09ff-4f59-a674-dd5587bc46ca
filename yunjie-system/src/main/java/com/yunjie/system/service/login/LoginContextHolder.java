package com.yunjie.system.service.login;

/**
 * 登录用户信息
 *
 */
public class LoginContextHolder {

    private final static ThreadLocal<LoginContext> HOLDER = ThreadLocal.withInitial(LoginContext::new);

    public static LoginContext getLoginContext() {
        return HOLDER.get();
    }

    public static void setLoginContext(LoginContext loginContext) {
        HOLDER.set(loginContext);
    }

    public static void cleanLoginContext() {
        HOLDER.remove();
    }
}
