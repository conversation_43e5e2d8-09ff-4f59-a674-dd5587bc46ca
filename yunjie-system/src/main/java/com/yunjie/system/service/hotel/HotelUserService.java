package com.yunjie.system.service.hotel;

import com.yunjie.common.core.domain.R;
import com.yunjie.system.model.request.hotel.user.*;
import com.yunjie.system.model.response.hotel.GetHotelUserResponse;

import java.util.List;

public interface HotelUserService {

    /**
     * 新增酒店用户信息
     * @param request
     * @return
     */
    R<GetHotelUserResponse> insertHotelUser(InsertHotelUserRequest request);

    /**
     * 更新酒店用户信息
     * @param request
     * @return
     */
    int updateHotelUser(UpdateHotelUserRequest request);

    /**
     * 变更酒店用户信息状态
     * @param request
     * @return
     */
    int changeStatus(ChangeHotelUserStatusRequest request);

    /**
     * 删除酒店用户信息
     * @param request
     * @return
     */
    int deleteHotelUser(DeleteHotelUserRequest request);

    /**
     * 获取酒店用户信息
     * @param request
     * @return
     */
    GetHotelUserResponse getHotelUser(GetHotelUserRequest request);

    /**
     * 获取酒店用户信息列表
     * @param request
     * @return
     */
    List<GetHotelUserResponse> getHotelUserList(GetHotelUserListRequest request);
}
