package com.yunjie.system.model.request.hotel.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增酒店渠道信息入参
 */
@Data
public class InsertHotelChannelRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID", required = true)
    @NotNull(message = "酒店ID不能为空")
    @Min(value = 1, message = "酒店ID不能小于1")
    private Long hotelId;

    /**
     * 渠道种类：1-携程，2-美团，3-飞猪，4-去哪儿，5-艺龙，6-京东，7-其他
     */
    @ApiModelProperty(value = "渠道种类：1-携程，2-美团，3-飞猪，4-去哪儿，5-艺龙，6-京东，7-其他", required = true)
    @NotNull(message = "渠道种类不能为空")
    @Min(value = 1, message = "渠道种类不能小于1")
    @Max(value = 7, message = "渠道种类不能大于7")
    private Integer channelType;

    /**
     * 渠道账号
     */
    @ApiModelProperty(value = "渠道账号", required = true)
    @NotBlank(message = "渠道账号不能为空")
    private String accountId;

    /**
     * 渠道密码
     */
    @ApiModelProperty(value = "渠道密码", required = true)
    @NotBlank(message = "渠道密码不能为空")
    private String password;
}
