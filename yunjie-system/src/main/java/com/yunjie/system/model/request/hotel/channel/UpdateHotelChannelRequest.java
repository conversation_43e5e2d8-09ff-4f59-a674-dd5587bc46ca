package com.yunjie.system.model.request.hotel.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新酒店渠道信息入参
 */
@Data
public class UpdateHotelChannelRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    @Min(value = 1, message = "主键ID不能小于1")
    private Long id;

    /**
     * 渠道账号
     */
    @ApiModelProperty("渠道账号")
    private String accountId;

    /**
     * 渠道密码
     */
    @ApiModelProperty("渠道密码")
    private String password;
}
