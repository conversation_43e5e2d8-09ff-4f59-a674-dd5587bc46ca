package com.yunjie.system.model.request.hotel.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 删除酒店用户信息入参
 */
@Data
public class DeleteHotelUserRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID集合
     */
    @ApiModelProperty(value = "用户ID", required = true)
    private Long[] userIds;

    /**
     * 登录用户名
     */
    @ApiModelProperty("登录用户名")
    private String userName;
}
