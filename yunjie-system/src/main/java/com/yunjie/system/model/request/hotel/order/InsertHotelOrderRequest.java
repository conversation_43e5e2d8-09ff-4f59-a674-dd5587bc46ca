package com.yunjie.system.model.request.hotel.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 新增酒店订单信息入参
 */
@Data
public class InsertHotelOrderRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Integer hotelId;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String orderTel;

    /**
     * 预定人
     */
    @ApiModelProperty(value = "预定人", required = true)
    @NotBlank(message = "预定人不能为空")
    private String orderName;

    /**
     * 订单来源 1-前台 2- 携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他
     */
    @ApiModelProperty(value = "订单来源 1-前台 2- 携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他", required = true)
    @NotNull(message = "订单来源不能为空")
    @Min(value = 1, message = "订单来源不能小于1")
    private Integer orderChannel;

    /**
     * 房间类型
     */
    @ApiModelProperty("房间类型")
    private Integer roomType;

    /**
     * 房间类型名称
     */
    @ApiModelProperty("房间类型名称")
    private String roomTypeName;

    /**
     * 房间数
     */
    @ApiModelProperty(value = "房间数", required = true)
    @NotNull(message = "房间数不能为空")
    @Min(value = 1, message = "房间数不能小于1")
    private Integer roomNum;

    /**
     * 预抵时间
     */
    @ApiModelProperty("预抵时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arriveTime;

    /**
     * 入住天数
     */
    @ApiModelProperty("入住天数")
    private Integer days;

    /**
     * 预离时间
     */
    @ApiModelProperty("预离时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date leaveTime;

    /**
     * 保留时间
     */
    @ApiModelProperty("保留时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date keepTime;

    /**
     * 订单总价
     */
    @ApiModelProperty(value = "订单总价", required = true)
    @NotNull(message = "订单总价不能为空")
    @Min(value = 0, message = "订单总价不能小于0")
    private Integer orderPrice;

    /** 外部订单号 */
    @ApiModelProperty(value = "外部订单号", required = true)
    @NotBlank(message = "外部订单号不能为空")
    private String outNo;

    /** 订单备注 */
    @ApiModelProperty("订单备注")
    private String meno;

}
