package com.yunjie.system.model.request.hotel.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取酒店订单信息列表入参
 */
@Data
public class GetHotelOrderListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @ApiModelProperty("页大小")
    private Integer pageSize = 10;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Integer hotelId;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 酒店名称
     */
    @ApiModelProperty("酒店名称")
    private String hotelName;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 外部订单号
     */
    @ApiModelProperty("外部订单号")
    private String outNo;

    /**
     * 预定人
     */
    @ApiModelProperty("预定人")
    private String orderName;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String orderTel;

    /**
     * 订单来源 -1-全部 1-前台 2-携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他
     */
    @ApiModelProperty("订单来源 -1-全部 1-前台 2-携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他")
    private Integer orderChannel = -1;

    /**
     * 入住类型 -1-全部 1-正常 2-免费 3-自用
     */
    @ApiModelProperty("入住类型 -1-全部 1-正常 2-免费 3-自用")
    private Integer checkinType = -1;

    /**
     * 入住方式 -1-全部 1-全天 2-长包 3-时租 4-团队 5-尾房
     */
    @ApiModelProperty("入住方式 -1-全部 1-全天 2-长包 3-时租 4-团队 5-尾房")
    private Integer checkinModel = -1;

    /**
     * 订单状态 -1-全部 0-取消预定 1-待入住 2-已入住 3-挂退 4-离店 5-NoShow
     */
    @ApiModelProperty("订单状态 -1-全部 0-取消预定 1-待入住 2-已入住 3-挂退 4-离店 5-NoShow")
    private Integer orderStatus = -1;
}
