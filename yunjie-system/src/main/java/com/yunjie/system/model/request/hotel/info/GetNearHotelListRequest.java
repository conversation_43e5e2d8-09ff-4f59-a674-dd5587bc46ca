package com.yunjie.system.model.request.hotel.info;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 小程序端调用-获取附近酒店列表入参
 */
@Data
public class GetNearHotelListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @ApiModelProperty("页大小")
    private Integer pageSize = 10;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度", required = true)
    @NotNull(message = "经度不能为空")
    @DecimalMin(value = "0.00", message = "经度不能小于0")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @ApiModelProperty(value = "维度", required = true)
    @NotNull(message = "维度不能为空")
    @DecimalMin(value = "0.00", message = "维度不能小于0")
    private BigDecimal latitude;

    /**
     * 范围（米）
     */
    @ApiModelProperty(value = "范围（米）")
    @NotNull(message = "范围（米）不能为空")
    @DecimalMin(value = "0.00", message = "范围（米）不能小于0")
    private BigDecimal range;

}
