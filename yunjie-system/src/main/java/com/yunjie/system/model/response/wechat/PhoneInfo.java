package com.yunjie.system.model.response.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户手机号信息
 */
@Data
public class PhoneInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户绑定的手机号（国外手机号会有区号）
     */
    @ApiModelProperty("用户绑定的手机号（国外手机号会有区号）")
    private String phoneNumber;

    /**
     * 没有区号的手机号
     */
    @ApiModelProperty("没有区号的手机号")
    private String purePhoneNumber;

    /**
     * 区号
     */
    @ApiModelProperty("区号")
    private String countryCode;

    /**
     * 数据水印
     */
    @ApiModelProperty("数据水印")
    private WaterMark waterMark;
}
