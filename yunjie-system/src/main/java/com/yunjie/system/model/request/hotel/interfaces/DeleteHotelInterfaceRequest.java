package com.yunjie.system.model.request.hotel.interfaces;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 删除酒店PMS对接地址信息入参
 */
@Data
public class DeleteHotelInterfaceRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID集合
     */
    @ApiModelProperty(value = "主键ID", required = true)
    private Long[] ids;

    /**
     * 登录用户名
     */
    @ApiModelProperty("登录用户名")
    private String userName;
}
