package com.yunjie.system.model.request.hotel.info;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 新增酒店基本信息入参
 */
@Data
public class InsertHotelInfoRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店编码
     */
    @ApiModelProperty(value = "酒店编码", required = true)
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    /**
     * 酒店名称
     */
    @ApiModelProperty(value = "酒店名称", required = true)
    @NotBlank(message = "酒店名称不能为空")
    private String hotelName;

    /**
     * 酒店类型：1-经济型，2-商务型，3-豪华型，4-民宿，5-度假村
     */
    @ApiModelProperty("酒店类型：1-经济型，2-商务型，3-豪华型，4-民宿，5-度假村")
    private Integer hotelType;

    /**
     * 酒店星级（1~5星）
     */
    @ApiModelProperty("酒店星级（1~5星）")
    private Integer starLevel;

    /**
     * 酒店图片
     */
    @ApiModelProperty("酒店图片")
    private String hotelPic;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码", required = true)
    @NotBlank(message = "省编码不能为空")
    private String provinceCode;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码", required = true)
    @NotBlank(message = "市编码不能为空")
    private String cityCode;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码", required = true)
    @NotBlank(message = "区编码不能为空")
    private String districtCode;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message = "详细地址不能为空")
    private String detailAddress;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度", required = true)
    @NotNull(message = "经度不能为空")
    @DecimalMin(value = "0.00", message = "经度不能小于0")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @ApiModelProperty(value = "维度", required = true)
    @NotNull(message = "维度不能为空")
    @DecimalMin(value = "0.00", message = "维度不能小于0")
    private BigDecimal latitude;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", required = true)
    @NotBlank(message = "联系电话不能为空")
    private String telephone;

}
