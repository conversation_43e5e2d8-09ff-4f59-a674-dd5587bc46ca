package com.yunjie.system.model.response.wechat;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 获取微信信息出参
 */
@Data
public class GetWechatBaseInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Integer id;

    @ApiModelProperty(value = "微信应用类型：1-公众号，2-小程序")
    private Integer appType;

    @ApiModelProperty(value = "微信APPID")
    private String appId;

    @ApiModelProperty(value = "微信AppSecret")
    private String appSecret;

    @ApiModelProperty(value = "微信应用名称")
    private String appName;

    @ApiModelProperty(value = "微信状态：1-启用，2-停用")
    private int appStatus;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
