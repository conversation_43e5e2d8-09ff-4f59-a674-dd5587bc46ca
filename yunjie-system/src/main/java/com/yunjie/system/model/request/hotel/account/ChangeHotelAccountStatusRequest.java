package com.yunjie.system.model.request.hotel.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 变更酒店账号信息状态入参
 */
@Data
public class ChangeHotelAccountStatusRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    @Min(value = 1, message = "主键ID不能小于1")
    private Long id;

    /**
     * 状态：0-启用，1-停用
     */
    @ApiModelProperty(value = "状态：0-启用，1-停用", required = true)
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态不能小于0")
    @Max(value = 1, message = "状态不能大于1")
    private Integer status;
}
