package com.yunjie.system.model.request.hotel.ad;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增酒店广告信息入参
 */
@Data
public class InsertHotelAdRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID", required = true)
    @NotNull(message = "酒店ID不能为空")
    @Min(value = 1, message = "酒店ID不能小于1")
    private Long hotelId;

    /**
     * 广告名称
     */
    @ApiModelProperty(value = "广告名称", required = true)
    @NotBlank(message = "广告名称不能为空")
    private String adName;

    /**
     * 广告描述
     */
    @ApiModelProperty(value = "广告描述")
    private String adDesc;

    /**
     * 广告地址
     */
    @ApiModelProperty(value = "广告地址", required = true)
    @NotBlank(message = "广告地址不能为空")
    private String adUrl;

}
