package com.yunjie.system.model.request.hotel.channel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取酒店渠道信息列表入参
 */
@Data
public class GetHotelChannelListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @ApiModelProperty("页大小")
    private Integer pageSize = 10;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 酒店名称
     */
    @ApiModelProperty("酒店名称")
    private String hotelName;

    /**
     * 渠道种类：-1-全部，1-携程，2-美团，3-飞猪，4-去哪儿，5-艺龙，6-京东，7-其他
     */
    @ApiModelProperty("渠道种类：-1-全部，1-携程，2-美团，3-飞猪，4-去哪儿，5-艺龙，6-京东，7-其他")
    private Integer channelType = -1;

    /**
     * 状态：-1-全部，0-启用，1-停用
     */
    @ApiModelProperty("状态：-1-全部，0-启用，1-停用")
    private Integer status = -1;
}
