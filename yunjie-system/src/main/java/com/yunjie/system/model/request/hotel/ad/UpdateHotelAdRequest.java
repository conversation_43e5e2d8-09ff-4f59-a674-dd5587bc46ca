package com.yunjie.system.model.request.hotel.ad;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新酒店广告信息入参
 */
@Data
public class UpdateHotelAdRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    @Min(value = 1, message = "主键ID不能小于1")
    private Long id;

    /**
     * 广告名称
     */
    @ApiModelProperty(value = "广告名称")
    private String adName;

    /**
     * 广告描述
     */
    @ApiModelProperty(value = "广告描述")
    private String adDesc;

    /**
     * 广告地址
     */
    @ApiModelProperty(value = "广告地址")
    private String adUrl;

}
