package com.yunjie.system.model.response.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 获取酒店基本信息出参
 */
@Data
public class GetHotelInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 酒店名称
     */
    @ApiModelProperty("酒店名称")
    private String hotelName;

    /**
     * 酒店类型：1-经济型，2-商务型，3-豪华型，4-民宿，5-度假村
     */
    @ApiModelProperty("酒店类型：1-经济型，2-商务型，3-豪华型，4-民宿，5-度假村")
    private Integer hotelType;

    /**
     * 酒店星级（1~5星）
     */
    @ApiModelProperty("酒店星级（1~5星）")
    private Integer starLevel;

    /**
     * 酒店图片
     */
    @ApiModelProperty("酒店图片")
    private String hotelPic;

    /**
     * 省编码
     */
    @ApiModelProperty("省编码")
    private String provinceCode;

    /**
     * 省名称
     */
    @ApiModelProperty("省名称")
    private String provinceName;

    /**
     * 市编码
     */
    @ApiModelProperty("市编码")
    private String cityCode;

    /**
     * 市名称
     */
    @ApiModelProperty("市名称")
    private String cityName;

    /**
     * 区编码
     */
    @ApiModelProperty("区编码")
    private String districtCode;

    /**
     * 区名称
     */
    @ApiModelProperty("区名称")
    private String districtName;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String detailAddress;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @ApiModelProperty("维度")
    private BigDecimal latitude;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String telephone;

    /**
     * 状态：0-启用，1-停用
     */
    @ApiModelProperty("状态：0-启用，1-停用")
    private Integer status;

    /**
     * 删除标志：0-存在，2-删除
     */
    @ApiModelProperty("删除标志：0-存在，2-删除")
    private Integer delFlag;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 距离（米）
     */
    @ApiModelProperty("距离（米）")
    private Integer distance;
}
