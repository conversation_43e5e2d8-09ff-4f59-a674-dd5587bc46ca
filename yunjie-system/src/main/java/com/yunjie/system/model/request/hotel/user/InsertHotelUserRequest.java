package com.yunjie.system.model.request.hotel.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 新增酒店用户信息入参
 */
@Data
public class InsertHotelUserRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 昵称
     */
    @ApiModelProperty("昵称")
    private String nickName;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatarUrl;

    /**
     * 用户openid
     */
    @ApiModelProperty("用户openid")
    private String openid;

    /**
     * 用户unionid
     */
    @ApiModelProperty("用户unionid")
    private String unionId;

    /**
     * 微信appid
     */
    @ApiModelProperty("微信appid")
    private String appId;

    /**
     * 用户手机号
     */
    @ApiModelProperty("用户手机号")
    private String mobile;

    /**
     * 绑定标志：0-未绑定，1-已绑定
     */
    @ApiModelProperty("绑定标志：0-未绑定，1-已绑定")
    private Integer bindFlag;
}
