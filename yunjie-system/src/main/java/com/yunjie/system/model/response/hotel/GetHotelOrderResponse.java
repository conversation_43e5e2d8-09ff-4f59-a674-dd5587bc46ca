package com.yunjie.system.model.response.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 获取酒店订单信息出参
 */
@Data
public class GetHotelOrderResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 是否预定：1-是 0-否
     */
    @ApiModelProperty("是否预定：1-是 0-否")
    private Integer orderFlag;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Integer hotelId;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 酒店名称
     */
    @ApiModelProperty("酒店名称")
    private String hotelName;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String orderTel;

    /**
     * 预定人
     */
    @ApiModelProperty("预定人")
    private String orderName;

    /**
     * 订单来源 1-前台 2-携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他
     */
    @ApiModelProperty("订单来源 1-前台 2-携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他")
    private Integer orderChannel;

    /**
     * 房型
     */
    @ApiModelProperty("房型")
    private Integer roomType;

    /**
     * 房型名称
     */
    @ApiModelProperty("房型名称")
    private Integer roomTypeName;

    /**
     * 房间数
     */
    @ApiModelProperty("房间数")
    private Integer roomNum;

    /**
     * 入住类型 1-正常 2-免费 3-自用
     */
    @ApiModelProperty("入住类型 1-正常 2-免费 3-自用")
    private Integer checkinType;

    /**
     * 入住方式 1-全天 2-长包 3-时租 4-团队 5-尾房
     */
    @ApiModelProperty("入住方式 1-全天 2-长包 3-时租 4-团队 5-尾房")
    private Integer checkinModel;

    /**
     * 客源类型 散客 会员 公司 中介
     */
    @ApiModelProperty("客源类型 散客 会员 公司 中介")
    private Integer customType;

    /**
     * 计费规则（仅时租房用）
     */
    @ApiModelProperty("计费规则（仅时租房用）")
    private Integer hoursRule;

    /**
     * 预抵时间
     */
    @ApiModelProperty("预抵时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arriveTime;

    /**
     * 入住天数
     */
    @ApiModelProperty("入住天数")
    private Integer days;

    /**
     * 预离时间
     */
    @ApiModelProperty("预离时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date leaveTime;

    /**
     * 保留时间
     */
    @ApiModelProperty("保留时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date keepTime;

    /**
     * 订单总价
     */
    @ApiModelProperty("订单总价")
    private Integer orderPrice;

    /** 外部订单号 */
    @ApiModelProperty("外部订单号")
    private String outNo;

    /** 销售员ID */
    @ApiModelProperty("销售员ID")
    private String seller;

    /** 订单备注 */
    @ApiModelProperty("订单备注")
    private String meno;

    /** 订单状态  0-取消预定 1-待入住 2-已入住 3-挂退 4-离店 5-NoShow */
    @ApiModelProperty("订单状态  0-取消预定 1-待入住 2-已入住 3-挂退 4-离店 5-NoShow")
    private Integer orderStatus;

    /** 联房NO */
    @ApiModelProperty("联房NO")
    private String unionNo;

    /** OTA付款类型 0-预付 1-现付 */
    @ApiModelProperty("OTA付款类型 0-预付 1-现付")
    private Integer payTypeCode;

    /** OTA渠道 1-PMS 2-EBK 3-OTA 4-AIO 5-API 6-CRS */
    @ApiModelProperty("OTA渠道 1-PMS 2-EBK 3-OTA 4-AIO 5-API 6-CRS")
    private Integer otaChannel;

    /** 最后取消时间(OTA用) */
    @ApiModelProperty("最后取消时间(OTA用)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastCancelTime;

    /** 开票类型 0-无 1-无需开票 */
    @ApiModelProperty("开票类型 0-无 1-无需开票")
    private Integer invoiceType;

    /** 是否虚拟订单 0-不是 1-是 */
    @ApiModelProperty("是否虚拟订单 0-不是 1-是")
    private Integer isVirtual;

    /** 启用标识 1-启用 0-未启用 */
    @ApiModelProperty("启用标识 1-启用 0-未启用")
    private Integer enable;

    /** 删除标识 1-有效 0-删除 */
    @ApiModelProperty("删除标识 1-有效 0-删除")
    private Integer deleteFlag;

    /** 添加时间 */
    @ApiModelProperty("添加时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 添加人 */
    @ApiModelProperty("添加人")
    private String createBy;

    /** 修改时间 */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /** 修改人 */
    @ApiModelProperty("修改人")
    private String updatedBy;

    /** 住客来源：0-国内，1-境外 */
    @ApiModelProperty("住客来源：0-国内，1-境外")
    private Integer guestSource;
}
