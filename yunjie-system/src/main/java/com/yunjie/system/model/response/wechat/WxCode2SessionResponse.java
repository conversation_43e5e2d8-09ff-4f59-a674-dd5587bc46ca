package com.yunjie.system.model.response.wechat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信小程序登录出参
 */
@Data
public class WxCode2SessionResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 微信用户唯一标识
     */
    @ApiModelProperty("微信用户唯一标识")
    private String openid;
    
    /**
     * 用户在开放平台的唯一标识符
     */
    @ApiModelProperty("用户在开放平台的唯一标识符")
    private String unionid;

    /**
     * 会话密钥
     */
    @ApiModelProperty("会话密钥")
    private String session_key;
    
    /**
     * 错误信息，请求失败时返回
     */
    @ApiModelProperty("错误信息")
    private String errmsg;
    
    /**
     * 错误码，请求失败时返回
     */
    @ApiModelProperty("错误码")
    private Integer errcode;
}
