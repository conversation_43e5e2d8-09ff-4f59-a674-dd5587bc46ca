package com.yunjie.system.model.request.hotel.checkin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 新增酒店预入住信息入参
 */
@Data
public class InsertHotelCheckinRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 预订单ID */
    @ApiModelProperty(value = "预订单ID")
    private Long bookingId;

    /** 订单号 */
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /** 酒店ID */
    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    /** 酒店编码 */
    @ApiModelProperty(value = "酒店编码")
    private String hotelCode;

    /** 预入住的房间类型 */
    @ApiModelProperty(value = "预入住的房间类型")
    private Integer roomType;

    /** 预入住的房间类型名称 */
    @ApiModelProperty(value = "预入住的房间类型名称")
    private String roomTypeName;

    /** 预入住的房间ID */
    @ApiModelProperty(value = "预入住的房间ID")
    private Long roomId;

    /** 入住人姓名 */
    @ApiModelProperty(value = "入住人姓名")
    private String guestName;

    /** 证件类型：1-身份证，2-驾驶证，3-户口本，4-护照 */
    @ApiModelProperty(value = "证件类型：1-身份证，2-驾驶证，3-户口本，4-护照")
    private Integer idType;

    /** 证件号 */
    @ApiModelProperty(value = "证件号")
    private String idNumber;

    /**
     * 证件开始日期
     */
    @ApiModelProperty(value = "证件开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idStartDate;

    /**
     * 证件截止日期
     */
    @ApiModelProperty(value = "证件截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idEndDate;

    /**
     * 证件正面照
     */
    @ApiModelProperty(value = "证件正面照")
    private String idFrontPic;

    /**
     * 证件反面照
     */
    @ApiModelProperty(value = "证件反面照")
    private String idBackPic;

    /**
     * 现场照
     */
    @ApiModelProperty(value = "现场照")
    private String livePic;

    /**
     * 人脸照
     */
    @ApiModelProperty(value = "人脸照")
    private String facePic;

    /** 性别：1-男，2-女 */
    @ApiModelProperty(value = "性别：1-男，2-女")
    private Integer gender;

    /** 出生日期 */
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    /** 户籍地址 */
    @ApiModelProperty(value = "户籍地址")
    private String address;

    /** 实际入住时间 */
    @ApiModelProperty(value = "实际入住时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkinTime;

    /** 预计退房时间 */
    @ApiModelProperty(value = "预计退房时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedCheckoutTime;

    /** 实际退房时间 */
    @ApiModelProperty(value = "实际退房时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkoutTime;

    /** 入住天数 */
    @ApiModelProperty(value = "入住天数")
    private Integer stayDays;
}
