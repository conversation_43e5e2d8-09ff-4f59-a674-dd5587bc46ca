package com.yunjie.system.model.request.hotel.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 获取酒店用户信息入参
 */
@Data
public class GetHotelUserRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    @Min(value = 1, message = "用户ID不能小于1")
    private Long userId;

    /**
     * 用户openid
     */
    @ApiModelProperty("用户openid")
    private String openid;

    /**
     * 用户unionid
     */
    @ApiModelProperty("用户unionid")
    private String unionId;

    /**
     * 用户手机号
     */
    @ApiModelProperty("用户手机号")
    private String mobile;

    /**
     * 微信appid
     */
    @ApiModelProperty("微信appid")
    private String appId;
}
