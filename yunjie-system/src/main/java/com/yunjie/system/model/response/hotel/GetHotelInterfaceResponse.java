package com.yunjie.system.model.response.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 获取酒店PMS对接地址信息出参
 */
@Data
public class GetHotelInterfaceResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 酒店ID
     */
    @ApiModelProperty("酒店ID")
    private Long hotelId;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 酒店名称
     */
    @ApiModelProperty("酒店名称")
    private String hotelName;

    /**
     * 域名地址
     */
    @ApiModelProperty(value = "域名地址")
    private String domainUrl;

    /**
     * 接口地址
     */
    @ApiModelProperty(value = "接口地址")
    private String interfaceUrl;

    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述")
    private String interfaceDesc;

    /**
     * 状态：0-启用，1-停用
     */
    @ApiModelProperty("状态：0-启用，1-停用")
    private Integer status;

    /**
     * 删除标志：0-存在，2-删除
     */
    @ApiModelProperty("删除标志：0-存在，2-删除")
    private Integer delFlag;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
