package com.yunjie.system.model.response.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 数据水印
 */
@Data
public class WaterMark implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户获取手机号操作的时间戳
     */
    @ApiModelProperty("用户获取手机号操作的时间戳")
    private Integer timestamp;

    /**
     * 小程序appid
     */
    @ApiModelProperty("小程序appid")
    private String appid;
}
