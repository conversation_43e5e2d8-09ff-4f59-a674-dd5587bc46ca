package com.yunjie.system.model.request.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 获取accesstoken入参
 */
@Data
public class GetAccessTokenRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 微信APPID
     */
    @ApiModelProperty(value = "微信APPID", required = true)
    @NotBlank(message = "微信APPID不能为空")
    private String appId;
}
