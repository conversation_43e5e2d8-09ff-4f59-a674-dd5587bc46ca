package com.yunjie.system.model.request.hotel.booking;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 新增酒店预订信息入参
 */
@Data
public class InsertHotelBookingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 酒店ID */
    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    /** 房型ID */
    @ApiModelProperty(value = "房型ID")
    private Integer roomTypeId;

    /** 预订的房间数量 */
    @ApiModelProperty(value = "预订的房间数量")
    private Integer roomCount;

    /** 预定人 */
    @ApiModelProperty(value = "预定人")
    private String contactName;

    /** 预定人电话 */
    @ApiModelProperty(value = "预定人电话")
    private String contactPhone;

    /** 入住日期 */
    @ApiModelProperty(value = "入住日期")
    private Date checkinDate;

    /** 离店日期 */
    @ApiModelProperty(value = "离店日期")
    private Date checkoutDate;

    /** 预计到店时间 */
    @ApiModelProperty(value = "预计到店时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arrivalTime;

    /** 客户备注 */
    @ApiModelProperty(value = "客户备注")
    private String remark;
}
