package com.yunjie.system.model.request.hotel.info;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 更新酒店基本信息入参
 */
@Data
public class UpdateHotelInfoRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    @Min(value = 1, message = "主键ID不能小于1")
    private Long id;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 酒店名称
     */
    @ApiModelProperty("酒店名称")
    private String hotelName;

    /**
     * 酒店类型：1-经济型，2-商务型，3-豪华型，4-民宿，5-度假村
     */
    @ApiModelProperty("酒店类型：1-经济型，2-商务型，3-豪华型，4-民宿，5-度假村")
    private Integer hotelType;

    /**
     * 酒店星级（1~5星）
     */
    @ApiModelProperty("酒店星级（1~5星）")
    private Integer starLevel;

    /**
     * 酒店图片
     */
    @ApiModelProperty("酒店图片")
    private String hotelPic;

    /**
     * 省编码
     */
    @ApiModelProperty("省编码")
    private String provinceCode;

    /**
     * 市编码
     */
    @ApiModelProperty("市编码")
    private String cityCode;

    /**
     * 区编码
     */
    @ApiModelProperty("区编码")
    private String districtCode;

    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String detailAddress;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private BigDecimal longitude;

    /**
     * 维度
     */
    @ApiModelProperty("维度")
    private BigDecimal latitude;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String telephone;

}
