package com.yunjie.system.model.request.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 微信小程序登录入参
 */
@Data
public class WechatLoginRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 登录时获取的code
     */
    @ApiModelProperty(value = "登录时获取的code", required = true)
    @NotBlank(message = "登录时获取的code不能为空")
    private String jsCode;

    /**
     * 微信APPID
     */
    @ApiModelProperty(value = "微信APPID", required = true)
    @NotBlank(message = "微信APPID不能为空")
    private String appId;
}
