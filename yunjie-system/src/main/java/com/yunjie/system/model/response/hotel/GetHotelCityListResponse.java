package com.yunjie.system.model.response.hotel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 获取省市区列表出参
 */
@Data
public class GetHotelCityListResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 行政区划编码
     */
    @ApiModelProperty("行政区划编码")
    private String id;

    /**
     * 行政区划名称
     */
    @ApiModelProperty("行政区划名称")
    private String name;

    /**
     * 行政区划上级ID
     */
    @ApiModelProperty("行政区划上级ID")
    private Long pid;

    /**
     * 行政区划层级level
     */
    @ApiModelProperty("行政区划层级level")
    private Integer lv;

    private List<GetHotelCityListResponse> children = new ArrayList<>();

}
