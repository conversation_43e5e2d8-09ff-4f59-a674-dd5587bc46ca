package com.yunjie.system.model.response.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 酒店用户信息
 */
@Data
public class HotelUserResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 昵称
     */
    @ApiModelProperty("昵称")
    private String nickName;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatarUrl;

    /**
     * 用户openid
     */
    @ApiModelProperty("用户openid")
    private String openid;

    /**
     * 用户unionid
     */
    @ApiModelProperty("用户unionid")
    private String unionId;

    /**
     * 用户手机号
     */
    @ApiModelProperty("用户手机号")
    private String mobile;

    /**
     * 状态：0-启用，1-停用
     */
    @ApiModelProperty("状态：0-启用，1-停用")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 登录验证
     */
    @ApiModelProperty("登录验证")
    private String token;
}
