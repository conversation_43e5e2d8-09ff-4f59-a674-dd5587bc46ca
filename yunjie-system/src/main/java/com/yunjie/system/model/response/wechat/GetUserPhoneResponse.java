package com.yunjie.system.model.response.wechat;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取用户手机号出参
 */
@Data
public class GetUserPhoneResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码，请求失败时返回
     */
    @JsonProperty("errcode")
    @ApiModelProperty("错误码")
    private Integer errCode;

    /**
     * 错误信息，请求失败时返回
     */
    @JsonProperty("errmsg")
    @ApiModelProperty("错误信息")
    private String errMsg;

    /**
     * 用户手机号信息
     */
    @JsonProperty("phone_info")
    @ApiModelProperty("用户手机号信息")
    private PhoneInfo phoneInfo;
}
