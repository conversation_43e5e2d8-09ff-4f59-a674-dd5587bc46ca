package com.yunjie.system.model.request.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 获取用户手机号入参
 */
@Data
public class GetUserPhoneRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号获取凭证code
     */
    @ApiModelProperty(value = "手机号获取凭证code", required = true)
    @NotBlank(message = "手机号获取凭证code不能为空")
    private String code;

    /**
     * 微信APPID
     */
    @ApiModelProperty(value = "微信APPID", required = true)
    @NotBlank(message = "微信APPID不能为空")
    private String appId;
}
