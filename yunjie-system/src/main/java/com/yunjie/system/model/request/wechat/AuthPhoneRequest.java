package com.yunjie.system.model.request.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 授权手机号注册入参
 */
@Data
public class AuthPhoneRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号码不能为空")
    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;

    /**
     * 微信APPID
     */
    @NotBlank(message = "appId不能为空")
    @ApiModelProperty(value = "微信APPID", required = true)
    private String appId;

    /**
     * 用户openid
     */
    @NotBlank(message = "openId不能为空")
    @ApiModelProperty(value = "用户openid", required = true)
    private String openId;

    /**
     * 用户unionid
     */
    @ApiModelProperty("用户unionid")
    private String unionId;
}
