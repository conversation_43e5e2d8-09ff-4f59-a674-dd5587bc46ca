package com.yunjie.system.model.request.hotel.knowledge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新酒店知识库信息入参
 */
@Data
public class UpdateHotelKnowledgeRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    @Min(value = 1, message = "主键ID不能小于1")
    private Long id;

    /**
     * 知识库名称
     */
    @ApiModelProperty(value = "知识库名称")
    private String knowledgeName;

    /**
     * 知识库描述
     */
    @ApiModelProperty(value = "知识库描述")
    private String knowledgeDesc;

    /**
     * 知识库类型：
     */
    @ApiModelProperty(value = "知识库类型：")
    private Integer knowledgeType;

    /**
     * 知识库内容
     */
    @ApiModelProperty(value = "知识库内容")
    private String knowledgeContent;
}
