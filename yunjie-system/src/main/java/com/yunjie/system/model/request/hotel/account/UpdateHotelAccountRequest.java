package com.yunjie.system.model.request.hotel.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 更新酒店账号信息入参
 */
@Data
public class UpdateHotelAccountRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    @Min(value = 1, message = "主键ID不能小于1")
    private Long id;

    /**
     * 登录账号
     */
    @ApiModelProperty("登录账号")
    private String loginName;

    /**
     * 登录密码
     */
    @ApiModelProperty("登录密码")
    private String loginPwd;

    /**
     * 员工姓名
     */
    @ApiModelProperty("员工姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
    private String identityCard;
}
