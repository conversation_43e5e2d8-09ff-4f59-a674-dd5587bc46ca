package com.yunjie.system.model.response.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class GetHotelBookingResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 酒店ID */
    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    /** 酒店名称 */
    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    /** 房型ID */
    @ApiModelProperty(value = "房型ID")
    private Integer roomTypeId;

    /** 预订的房间数量 */
    @ApiModelProperty(value = "预订的房间数量")
    private Integer roomCount;

    /** 预定人 */
    @ApiModelProperty(value = "预定人")
    private String contactName;

    /** 预定人电话 */
    @ApiModelProperty(value = "预定人电话")
    private String contactPhone;

    /** 入住日期 */
    @ApiModelProperty(value = "入住日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkinDate;

    /** 离店日期 */
    @ApiModelProperty(value = "离店日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkoutDate;

    /** 预计到店时间 */
    @ApiModelProperty(value = "预计到店时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arrivalTime;

    /** 状态：1-已预定，2-已取消 */
    @ApiModelProperty(value = "状态：1-已预定，2-已取消")
    private Integer status;

    /** 客户备注 */
    @ApiModelProperty(value = "客户备注")
    private String remark;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新人 */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
