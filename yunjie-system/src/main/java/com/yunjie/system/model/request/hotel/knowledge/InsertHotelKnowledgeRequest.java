package com.yunjie.system.model.request.hotel.knowledge;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增酒店知识库信息入参
 */
@Data
public class InsertHotelKnowledgeRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID", required = true)
    @NotNull(message = "酒店ID不能为空")
    @Min(value = 1, message = "酒店ID不能小于1")
    private Long hotelId;

    /**
     * 知识库名称
     */
    @ApiModelProperty(value = "知识库名称", required = true)
    @NotBlank(message = "知识库名称不能为空")
    private String knowledgeName;

    /**
     * 知识库描述
     */
    @ApiModelProperty(value = "知识库描述", required = true)
    @NotBlank(message = "知识库描述不能为空")
    private String knowledgeDesc;

    /**
     * 知识库类型：
     */
    @ApiModelProperty(value = "知识库类型：", required = true)
    @NotNull(message = "知识库类型不能为空")
    @Min(value = 1, message = "知识库类型不能小于1")
    private Integer knowledgeType;

    /**
     * 知识库内容
     */
    @ApiModelProperty(value = "知识库内容", required = true)
    @NotBlank(message = "知识库内容不能为空")
    private String knowledgeContent;

}
