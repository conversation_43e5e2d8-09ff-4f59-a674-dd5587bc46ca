package com.yunjie.system.model.response.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 获取酒店预入住信息出参
 */
@Data
public class GetHotelCheckinResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 预订单ID */
    @ApiModelProperty(value = "预订单ID")
    private Long bookingId;

    /** 订单号 */
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /** 酒店ID */
    @ApiModelProperty(value = "酒店ID")
    private Long hotelId;

    /** 酒店编码 */
    @ApiModelProperty(value = "酒店编码")
    private String hotelCode;

    /** 酒店名称 */
    @ApiModelProperty(value = "酒店名称")
    private String hotelName;

    /** 预入住的房间类型 */
    @ApiModelProperty(value = "预入住的房间类型")
    private Integer roomType;

    /** 预入住的房间类型名称 */
    @ApiModelProperty(value = "预入住的房间类型名称")
    private String roomTypeName;

    /** 实际入住的房间ID */
    @ApiModelProperty(value = "实际入住的房间ID")
    private Long roomId;

    /** 入住人姓名 */
    @ApiModelProperty(value = "入住人姓名")
    private String guestName;

    /** 证件类型：1-身份证，2-驾驶证，3-户口本，4-护照 */
    @ApiModelProperty(value = "证件类型：1-身份证，2-驾驶证，3-户口本，4-护照")
    private Integer idType;

    /** 证件号 */
    @ApiModelProperty(value = "证件号")
    private String idNumber;

    /** 性别：1-男，2-女 */
    @ApiModelProperty(value = "性别：1-男，2-女")
    private Integer gender;

    /** 出生日期 */
    @ApiModelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthDate;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    /** 户籍地址 */
    @ApiModelProperty(value = "户籍地址")
    private String address;

    /** 实际入住时间 */
    @ApiModelProperty(value = "实际入住时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkinTime;

    /** 预计退房时间 */
    @ApiModelProperty(value = "预计退房时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedCheckoutTime;

    /** 实际退房时间 */
    @ApiModelProperty(value = "实际退房时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkoutTime;

    /** 入住天数 */
    @ApiModelProperty(value = "入住天数")
    private Integer stayDays;

    /** 状态：1-未入住，2-预入住，3-已入住，4-已取消 */
    @ApiModelProperty(value = "状态：1-未入住，2-预入住，3-已入住，4-已取消")
    private Integer status;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新人 */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 推送状态：0-无需推送，1-未推送，2-已推送
     */
    private Integer pushStatus;

    /**
     * 推送结果
     */
    private String pushResult;

    /**
     * 短信状态：0-无需发送，1-未发送，2-已发送
     */
    private Integer smsStatus;
}
