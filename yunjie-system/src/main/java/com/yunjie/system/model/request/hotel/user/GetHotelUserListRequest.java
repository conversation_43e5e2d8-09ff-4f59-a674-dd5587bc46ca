package com.yunjie.system.model.request.hotel.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取酒店用户信息列表入参
 */
@Data
public class GetHotelUserListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @ApiModelProperty("页大小")
    private Integer pageSize = 10;

    /**
     * 用户openid
     */
    @ApiModelProperty("用户openid")
    private String openid;

    /**
     * 用户手机号
     */
    @ApiModelProperty("用户手机号")
    private String mobile;

    /**
     * 状态：-1-全部，0-启用，1-停用
     */
    @ApiModelProperty("状态：-1-全部，0-启用，1-停用")
    private Integer status = -1;
}
