package com.yunjie.system.model.request.hotel.checkin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 更新酒店预入住信息
 */
@Data
public class UpdateHotelCheckinRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /** 订单号 */
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    /** 入住人姓名 */
    @ApiModelProperty(value = "入住人姓名")
    private String guestName;

    /**
     * 状态：1-未入住，2-预入住，3-已入住，4-已取消
     */
    @ApiModelProperty(value = "状态：1-未入住，2-预入住，3-已入住，4-已取消")
    private Integer status;
}
