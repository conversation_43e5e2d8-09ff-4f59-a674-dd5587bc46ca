package com.yunjie.system.model.request.hotel.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 获取酒店账号信息列表入参
 */
@Data
public class GetHotelAccountListRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @ApiModelProperty("页大小")
    private Integer pageSize = 10;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 酒店名称
     */
    @ApiModelProperty("酒店名称")
    private String hotelName;

    /**
     * 员工姓名
     */
    @ApiModelProperty("员工姓名")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String mobile;

    /**
     * 状态：-1-全部，0-启用，1-停用
     */
    @ApiModelProperty("状态：-1-全部，0-启用，1-停用")
    private Integer status = -1;
}
