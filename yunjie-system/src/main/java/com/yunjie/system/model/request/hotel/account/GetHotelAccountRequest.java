package com.yunjie.system.model.request.hotel.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 获取酒店账号信息入参
 */
@Data
public class GetHotelAccountRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    @Min(value = 1, message = "主键ID不能小于1")
    private Long id;
}
