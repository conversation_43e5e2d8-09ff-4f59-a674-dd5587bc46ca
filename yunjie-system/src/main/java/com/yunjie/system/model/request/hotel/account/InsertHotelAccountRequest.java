package com.yunjie.system.model.request.hotel.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增酒店账号信息入参
 */
@Data
public class InsertHotelAccountRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID", required = true)
    @NotNull(message = "酒店ID不能为空")
    @Min(value = 1, message = "经度不能小于1")
    private Long hotelId;

    /**
     * 登录账号
     */
    @ApiModelProperty(value = "登录账号", required = true)
    @NotBlank(message = "登录账号不能为空")
    private String loginName;

    /**
     * 登录密码
     */
    @ApiModelProperty(value = "登录密码", required = true)
    @NotBlank(message = "登录密码不能为空")
    private String loginPwd;

    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "员工姓名", required = true)
    @NotBlank(message = "员工姓名不能为空")
    private String name;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String mobile;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号", required = true)
    @NotBlank(message = "身份证号不能为空")
    private String identityCard;
}
