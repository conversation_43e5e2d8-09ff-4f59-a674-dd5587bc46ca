package com.yunjie.system.model.request.hotel.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 更新酒店订单信息入参
 */
@Data
public class UpdateHotelOrderRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单来源 1-前台 2- 携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他
     */
    @ApiModelProperty(value = "订单来源 1-前台 2- 携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他", required = true)
    @NotNull(message = "订单来源不能为空")
    @Min(value = 1, message = "订单来源不能小于1")
    private Integer orderChannel;

    /** 外部订单号 */
    @ApiModelProperty(value = "外部订单号", required = true)
    @NotBlank(message = "外部订单号不能为空")
    private String outNo;

    /** 订单状态  0取消预定 1待入住 2已入住 3挂退 4离店 5NoShow */
    @ApiModelProperty("订单状态  0取消预定")
    @Max(value = 0, message = "订单状态不能大于0")
    private Integer orderStatus;

    /**
     * 酒店编码
     */
    @ApiModelProperty("酒店编码")
    private String hotelCode;

    /**
     * 预定人
     */
    @ApiModelProperty(value = "预定人", required = true)
    @NotBlank(message = "预定人不能为空")
    private String orderName;

    /**
     * 预抵时间
     */
    @ApiModelProperty("预抵时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arriveTime;

    /**
     * 入住天数
     */
    @ApiModelProperty("入住天数")
    private Integer days;

    /**
     * 预离时间
     */
    @ApiModelProperty("预离时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date leaveTime;
}
