package com.yunjie.system.model.request.hotel.interfaces;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增酒店PMS对接地址信息入参
 */
@Data
public class InsertHotelInterfaceRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酒店ID
     */
    @ApiModelProperty(value = "酒店ID", required = true)
    @NotNull(message = "酒店ID不能为空")
    @Min(value = 1, message = "酒店ID不能小于1")
    private Long hotelId;

    /**
     * 域名地址
     */
    @ApiModelProperty(value = "域名地址", required = true)
    @NotBlank(message = "域名地址不能为空")
    private String domainUrl;

    /**
     * 接口地址
     */
    @ApiModelProperty(value = "接口地址", required = true)
    @NotBlank(message = "接口地址不能为空")
    private String interfaceUrl;

    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述")
    private String interfaceDesc;

}
