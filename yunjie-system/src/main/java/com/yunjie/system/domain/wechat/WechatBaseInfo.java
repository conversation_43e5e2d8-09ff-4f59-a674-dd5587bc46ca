package com.yunjie.system.domain.wechat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信信息配置表
 */
@TableName(value = "wechat_base_info")
@Data
public class WechatBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 微信应用类型：1-公众号，2-小程序
     */
    private Integer appType;

    /**
     * 微信APPID
     */
    private String appId;

    /**
     * 微信AppSecret
     */
    private String appSecret;

    /**
     * 微信应用名称
     */
    private String appName;

    /**
     * 微信状态：1-启用，2-停用
     */
    private Integer appStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;
}
