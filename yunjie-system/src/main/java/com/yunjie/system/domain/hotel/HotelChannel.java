package com.yunjie.system.domain.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 酒店渠道信息表
 */
@TableName(value = "hotel_channel")
@Data
public class HotelChannel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 渠道种类：1-携程，2-美团，3-飞猪，4-去哪儿，5-艺龙，6-京东，7-其他
     */
    private Integer channelType;

    /**
     * 渠道账号
     */
    private String accountId;

    /**
     * 渠道密码
     */
    private String password;

    /**
     * 状态：0-启用，1-停用
     */
    private Integer status;

    /**
     * 删除标志：0-存在，2-删除
     */
    private Integer delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
