package com.yunjie.system.domain.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 酒店预入住信息表
 */
@TableName(value = "hotel_checkin")
@Data
public class HotelCheckin implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 预订单ID */
    private Long bookingId;

    /** 订单号 */
    private String orderNo;

    /** 酒店ID */
    private Long hotelId;

    /** 酒店编码 */
    private String hotelCode;

    /** 预入住的房间类型 */
    private Integer roomType;

    /** 预入住的房间类型名称 */
    private String roomTypeName;

    /** 预入住的房间ID */
    private Long roomId;

    /** 入住人姓名 */
    private String guestName;

    /** 证件类型：1-身份证，2-驾驶证，3-户口本，4-护照 */
    private Integer idType;

    /** 证件号 */
    private String idNumber;

    /**
     * 证件开始日期
     */
    private Date idStartDate;

    /**
     * 证件截止日期
     */
    private Date idEndDate;

    /**
     * 证件正面照
     */
    private String idFrontPic;

    /**
     * 证件反面照
     */
    private String idBackPic;

    /**
     * 现场照
     */
    private String livePic;

    /**
     * 人脸照
     */
    private String facePic;

    /** 性别：1-男，2-女 */
    private Integer gender;

    /** 出生日期 */
    private Date birthDate;

    /** 联系电话 */
    private String phoneNumber;

    /** 户籍地址 */
    private String address;

    /** 实际入住时间 */
    private Date checkinTime;

    /** 预计退房时间 */
    private Date expectedCheckoutTime;

    /** 实际退房时间 */
    private Date checkoutTime;

    /** 入住天数 */
    private Integer stayDays;

    /** 状态：1-未入住，2-预入住，3-已入住，4-已取消 */
    private Integer status;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /**
     * 推送状态：0-无需推送，1-未推送，2-已推送
     */
    private Integer pushStatus;

    /**
     * 推送结果
     */
    private String pushResult;

    /**
     * 短信状态：0-无需发送，1-未发送，2-已发送
     */
    private Integer smsStatus;
}
