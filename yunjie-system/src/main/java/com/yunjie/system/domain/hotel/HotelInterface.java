package com.yunjie.system.domain.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 酒店PMS对接地址信息表
 */
@TableName(value = "hotel_interface")
@Data
public class HotelInterface implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 域名地址
     */
    private String domainUrl;

    /**
     * 接口地址
     */
    private String interfaceUrl;

    /**
     * 接口描述
     */
    private String interfaceDesc;

    /**
     * 状态：0-启用，1-停用
     */
    private Integer status;

    /**
     * 删除标志：0-存在，2-删除
     */
    private Integer delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
