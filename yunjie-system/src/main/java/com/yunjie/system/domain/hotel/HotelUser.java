package com.yunjie.system.domain.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 酒店用户信息表
 */
@TableName(value = "hotel_user")
@Data
public class HotelUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long userId;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 用户openid
     */
    private String openid;

    /**
     * 用户unionid
     */
    private String unionId;

    /**
     * 微信appid
     */
    private String appId;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 绑定标志：0-未绑定，1-已绑定
     */
    private Integer bindFlag;

    /**
     * 状态：0-启用，1-停用
     */
    private Integer status;

    /**
     * 删除标志：0-存在，2-删除
     */
    private Integer delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
