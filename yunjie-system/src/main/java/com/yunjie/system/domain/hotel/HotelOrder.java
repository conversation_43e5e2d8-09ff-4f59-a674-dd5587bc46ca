package com.yunjie.system.domain.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 酒店订单信息表
 */
@TableName(value = "hotel_order")
@Data
public class HotelOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 是否预定：1-是  0-否
     */
    private Integer orderFlag;

    /**
     * 酒店ID
     */
    private Integer hotelId;

    /**
     * 酒店编码
     */
    private Integer hotelCode;

    /**
     * 手机号
     */
    private String orderTel;

    /**
     * 预定人
     */
    private String orderName;

    /**
     * 订单来源 1-前台 2- 携程 3-美团 4-飞猪 5-去哪儿 6-艺龙 7-其他
     */
    private Integer orderChannel;

    /**
     * 入住类型 1-正常 2-免费 3-自用
     */
    private Integer checkinType;

    /**
     * 入住方式 1-全天 2-长包  3-时租 4-团队 5-尾房
     */
    private Integer checkinModel;

    /**
     * 客源类型 散客 会员 公司 中介
     */
    private Integer customType;

    /**
     * 计费规则（仅时租房用）
     */
    private Integer hoursRule;

    /** 房间类型 */
    private Integer roomType;

    /** 房间类型名称 */
    private String roomTypeName;

    /**
     * 房间数
     */
    private Integer roomNum;

    /**
     * 预抵时间
     */
    private Date arriveTime;

    /**
     * 入住天数
     */
    private Integer days;

    /**
     * 预离时间
     */
    private Date leaveTime;

    /**
     * 保留时间
     */
    private Date keepTime;

    /**
     * 订单总价
     */
    private Integer orderPrice;

    /** 外部订单号 */
    private String outNo;

    /** 销售员ID */
    private String seller;

    /** 订单备注 */
    private String meno;

    /** 订单状态  0取消预定 1待入住 2已入住 3挂退 4离店 5NoShow */
    private Integer orderStatus;

    /** 联房NO */
    private String unionNo;

    /** OTA付款类型 0-预付 1-现付 */
    private Integer payTypeCode;

    /** OTA渠道 1-PMS 2-EBK 3-OTA 4-AIO 5-API 6-CRS */
    private Integer otaChannel;

    /** 最后取消时间(OTA用) */
    private Date lastCancelTime;

    /** 开票类型 0-无 1-无需开票 */
    private Integer invoiceType;

    /** 是否虚拟订单 0-不是 1-是 */
    private Integer isVirtual;

    /** 启用标识 1启用 0未启用 */
    private Integer enable;

    /** 删除标识 1有效 0删除 */
    private Integer deleteFlag;

    /** 添加时间 */
    private Date createdAt;

    /** 添加人 */
    private String createBy;

    /** 修改时间 */
    private Date updatedAt;

    /** 修改人 */
    private String updatedBy;

    /** 住客来源：0-国内，1-境外 */
    private Integer guestSource;

    /**
     * 推送状态：0-无需推送，1-未推送，2-已推送
     */
    private Integer pushStatus;

    /**
     * 推送结果
     */
    private String pushResult;

    /**
     * 短信状态：0-无需发送，1-未发送，2-已发送
     */
    private Integer smsStatus;

}
