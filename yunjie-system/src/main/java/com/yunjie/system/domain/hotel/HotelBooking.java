package com.yunjie.system.domain.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 酒店预订信息表
 */
@TableName(value = "hotel_booking")
@Data
public class HotelBooking implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 酒店ID */
    private Long hotelId;

    /** 房型ID */
    private Integer roomTypeId;

    /** 预订的房间数量 */
    private Integer roomCount;

    /** 预定人 */
    private String contactName;

    /** 预定人电话 */
    private String contactPhone;

    /** 入住日期 */
    private Date checkinDate;

    /** 离店日期 */
    private Date checkoutDate;

    /** 预计到店时间 */
    private Date arrivalTime;

    /** 状态：1-已预定，2-已取消 */
    private Integer status;

    /** 客户备注 */
    private String remark;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;
}
