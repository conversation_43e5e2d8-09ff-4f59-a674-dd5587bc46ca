package com.yunjie.system.domain.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店基础信息表
 */
@TableName(value = "hotel_info")
@Data
public class HotelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 酒店编码
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 酒店类型：1-经济型，2-商务型，3-豪华型，4-民宿，5-度假村
     */
    private Integer hotelType;

    /**
     * 酒店星级（1~5星）
     */
    private Integer starLevel;

    /**
     * 酒店图片
     */
    private String hotelPic;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区编码
     */
    private String districtCode;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 状态：0-启用，1-停用
     */
    private Integer status;

    /**
     * 删除标志：0-存在，2-删除
     */
    private Integer delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
