package com.yunjie.system.domain.sms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "sms_record")
@Data
public class SmsRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 短信接收方
     */
    private String mobile;

    /**
     * 短信类型：1-预订单入住提醒，2-预入住人提醒
     */
    private Integer smsType;

    /**
     * 短信内容
     */
    private String smsContent;

    /**
     * 状态：0-未发送，1-发送失败，2-已发送
     */
    private Integer smsStatus;

    /**
     * 短信发送结果
     */
    private String smsResult;

    /**
     * 外部关联单号
     */
    private String outNo;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;
}
