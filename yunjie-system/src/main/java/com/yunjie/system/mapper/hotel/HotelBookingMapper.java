package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelBooking;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingListRequest;
import com.yunjie.system.model.response.hotel.GetHotelBookingResponse;

import java.util.List;

public interface HotelBookingMapper extends BaseMapper<HotelBooking> {

    /**
     * 获取酒店预订信息列表
     * @param request
     * @return
     */
    List<GetHotelBookingResponse> getHotelBookingList(GetHotelBookingListRequest request);
}
