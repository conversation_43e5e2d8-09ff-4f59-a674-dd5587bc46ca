package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelInterface;
import com.yunjie.system.model.request.hotel.interfaces.DeleteHotelInterfaceRequest;
import com.yunjie.system.model.request.hotel.interfaces.GetHotelInterfaceListRequest;
import com.yunjie.system.model.response.hotel.GetHotelInterfaceResponse;

import java.util.List;

public interface HotelInterfaceMapper extends BaseMapper<HotelInterface> {

    /**
     * 批量删除
     * @param request
     * @return
     */
    int deleteHotelInterfaceByIds(DeleteHotelInterfaceRequest request);

    /**
     * 获取酒店PMS对接地址信息列表
     * @param request
     * @return
     */
    List<GetHotelInterfaceResponse> getHotelInterfaceList(GetHotelInterfaceListRequest request);
}
