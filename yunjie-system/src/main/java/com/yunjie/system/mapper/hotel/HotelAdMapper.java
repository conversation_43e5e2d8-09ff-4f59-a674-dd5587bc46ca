package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelAd;
import com.yunjie.system.model.request.hotel.ad.DeleteHotelAdRequest;
import com.yunjie.system.model.request.hotel.ad.GetHotelAdListRequest;
import com.yunjie.system.model.response.hotel.GetHotelAdResponse;

import java.util.List;

public interface HotelAdMapper extends BaseMapper<HotelAd> {

    /**
     * 批量删除
     * @param request
     * @return
     */
    int deleteHotelAdByIds(DeleteHotelAdRequest request);

    /**
     * 获取酒店广告信息列表
     * @param request
     * @return
     */
    List<GetHotelAdResponse> getHotelAdList(GetHotelAdListRequest request);
}
