package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelAccount;
import com.yunjie.system.model.request.hotel.account.DeleteHotelAccountRequest;
import com.yunjie.system.model.request.hotel.account.GetHotelAccountListRequest;
import com.yunjie.system.model.response.hotel.GetHotelAccountResponse;

import java.util.List;

public interface HotelAccountMapper extends BaseMapper<HotelAccount> {

    /**
     * 批量删除
     * @param request
     * @return
     */
    int deleteHotelAccountByIds(DeleteHotelAccountRequest request);

    /**
     * 获取酒店账号信息列表
     * @param request
     * @return
     */
    List<GetHotelAccountResponse> getHotelAccountList(GetHotelAccountListRequest request);
}
