package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelChannel;
import com.yunjie.system.model.request.hotel.channel.DeleteHotelChannelRequest;
import com.yunjie.system.model.request.hotel.channel.GetHotelChannelListRequest;
import com.yunjie.system.model.response.hotel.GetHotelChannelResponse;

import java.util.List;

public interface HotelChannelMapper extends BaseMapper<HotelChannel> {

    /**
     * 批量删除
     * @param request
     * @return
     */
    int deleteHotelChannelByIds(DeleteHotelChannelRequest request);

    /**
     * 获取酒店渠道信息列表
     * @param request
     * @return
     */
    List<GetHotelChannelResponse> getHotelChannelList(GetHotelChannelListRequest request);
}
