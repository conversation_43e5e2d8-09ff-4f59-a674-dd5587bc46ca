package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelUser;
import com.yunjie.system.model.request.hotel.user.DeleteHotelUserRequest;
import com.yunjie.system.model.request.hotel.user.GetHotelUserListRequest;
import com.yunjie.system.model.response.hotel.GetHotelUserResponse;

import java.util.List;

public interface HotelUserMapper extends BaseMapper<HotelUser> {

    /**
     * 批量删除
     * @param request
     * @return
     */
    int deleteHotelUserByIds(DeleteHotelUserRequest request);

    /**
     * 获取酒店渠道信息列表
     * @param request
     * @return
     */
    List<GetHotelUserResponse> getHotelUserList(GetHotelUserListRequest request);
}
