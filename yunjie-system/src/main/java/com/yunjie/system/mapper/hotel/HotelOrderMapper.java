package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelOrder;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderListRequest;
import com.yunjie.system.model.response.hotel.GetHotelOrderResponse;

import java.util.List;

public interface HotelOrderMapper extends BaseMapper<HotelOrder> {

    /**
     * 获取酒店订单信息列表
     * @param request
     * @return
     */
    List<GetHotelOrderResponse> getHotelOrderList(GetHotelOrderListRequest request);

    /**
     * 批量更新推送状态
     * @param list
     * @return
     */
    int updateBatchPush(List<HotelOrder> list);

    /**
     * 批量更新短信发送状态
     * @param list
     * @return
     */
    int updateBatchSMS(List<HotelOrder> list);
}
