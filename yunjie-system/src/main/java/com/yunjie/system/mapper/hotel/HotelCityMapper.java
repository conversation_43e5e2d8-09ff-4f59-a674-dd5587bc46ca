package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelCity;
import com.yunjie.system.model.request.hotel.city.GetHotelCityListRequest;
import com.yunjie.system.model.response.hotel.GetHotelCityListResponse;

import java.util.List;

public interface HotelCityMapper extends BaseMapper<HotelCity> {

    /**
     * 获取省市区列表数据
     * @param request
     * @return
     */
    List<GetHotelCityListResponse> getHotelCityList(GetHotelCityListRequest request);

}
