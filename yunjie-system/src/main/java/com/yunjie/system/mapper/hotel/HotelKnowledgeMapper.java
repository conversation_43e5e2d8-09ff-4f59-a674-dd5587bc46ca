package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelKnowledge;
import com.yunjie.system.model.request.hotel.knowledge.DeleteHotelKnowledgeRequest;
import com.yunjie.system.model.request.hotel.knowledge.GetHotelKnowledgeListRequest;
import com.yunjie.system.model.response.hotel.GetHotelKnowledgeResponse;

import java.util.List;

public interface HotelKnowledgeMapper extends BaseMapper<HotelKnowledge> {

    /**
     * 批量删除
     * @param request
     * @return
     */
    int deleteHotelKnowledgeByIds(DeleteHotelKnowledgeRequest request);

    /**
     * 获取酒店知识库信息列表
     * @param request
     * @return
     */
    List<GetHotelKnowledgeResponse> getHotelKnowledgeList(GetHotelKnowledgeListRequest request);
}
