package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelInfo;
import com.yunjie.system.model.request.hotel.info.DeleteHotelInfoRequest;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoListRequest;
import com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest;
import com.yunjie.system.model.request.hotel.info.GetNearHotelListRequest;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;

import java.util.List;

public interface HotelInfoMapper extends BaseMapper<HotelInfo> {

    /**
     * 批量删除
     * @param request
     * @return
     */
    int deleteHotelInfoByIds(DeleteHotelInfoRequest request);

    /**
     * 获取酒店基本信息
     * @param request
     * @return
     */
    GetHotelInfoResponse getHotelInfo(GetHotelInfoRequest request);

    /**
     * 获取酒店基本信息列表
     * @param request
     * @return
     */
    List<GetHotelInfoResponse> getHotelInfoList(GetHotelInfoListRequest request);

    /**
     * 获取附近酒店列表
     * @param request
     * @return
     */
    List<GetHotelInfoResponse> getNearHotelList(GetNearHotelListRequest request);
}
