package com.yunjie.system.mapper.hotel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yunjie.system.domain.hotel.HotelCheckin;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinListRequest;
import com.yunjie.system.model.response.hotel.GetHotelCheckinResponse;

import java.util.List;

public interface HotelCheckinMapper extends BaseMapper<HotelCheckin> {

    /**
     * 获取酒店入住信息列表
     * @param request
     * @return
     */
    List<GetHotelCheckinResponse> getHotelCheckinList(GetHotelCheckinListRequest request);

    /**
     * 获取预订单在第一个人入住后超过1小时还有人员还未入住的人员信息
     * @return
     */
    List<GetHotelCheckinResponse> getNoShowCheckinList();

    /**
     * 批量更新推送状态
     * @param list
     * @return
     */
    int updateBatchPush(List<HotelCheckin> list);

    /**
     * 批量更新短信发送状态
     * @param list
     * @return
     */
    int updateBatchSMS(List<GetHotelCheckinResponse> list);
}
