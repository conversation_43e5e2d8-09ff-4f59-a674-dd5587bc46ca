<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.sms.SmsRecordMapper">

    <!--批量添加-->
    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true">
        INSERT INTO
        sms_record(mobile,sms_type,sms_content,sms_status,sms_result,out_no,create_by,create_time)
        VALUES
        <foreach collection="list" item="sub" index="index" separator=",">
            (
            #{sub.mobile},
            #{sub.smsType},
            #{sub.smsContent},
            #{sub.smsStatus},
            #{sub.smsResult},
            #{sub.outNo},
            #{sub.createBy},
            #{sub.createTime}
            )
        </foreach>
    </insert>

</mapper>