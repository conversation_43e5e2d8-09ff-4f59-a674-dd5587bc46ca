<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelCheckinMapper">

    <select id="getHotelCheckinList" parameterType="com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelCheckinResponse">
    select hc.id,
           hc.user_id,
           hc.booking_id,
           hc.order_no,
           hc.hotel_id,
           hc.hotel_code,
           hi.hotel_name,
           hc.room_type,
           hc.room_type_name,
           hc.room_id,
           hc.guest_name,
           hc.id_type,
           hc.id_number,
           hc.gender,
           hc.birth_date,
           hc.phone_number,
           hc.address,
           hc.checkin_time,
           hc.expected_checkout_time,
           hc.checkout_time,
           hc.stay_days,
           hc.status,
           hc.create_by,
           hc.create_time,
           hc.update_by,
           hc.update_time,
           hc.push_status,
           hc.push_result,
           hc.sms_status
    from hotel_checkin hc
    left join hotel_info hi on hc.hotel_id = hi.id
    <where>
        <if test="hotelId != null and hotelId > 0">
            AND hc.hotel_id like concat('%', #{hotelId}, '%')
        </if>
        <if test="hotelName != null and hotelName != ''">
            AND hi.hotel_name like concat('%', #{hotelName}, '%')
        </if>
        <if test="userId != null and userId > 0">
            AND hc.user_id = #{userId}
        </if>
        <if test="status != null and status > 0">
            AND hc.status = #{status}
        </if>
    </where>
    order by hc.id desc
    </select>

    <select id="getNoShowCheckinList" resultType="com.yunjie.system.model.response.hotel.GetHotelCheckinResponse">
        select hc.id,
               hc.user_id,
               hc.booking_id,
               hc.order_no,
               hc.hotel_id,
               hc.hotel_code,
               hi.hotel_name,
               hc.room_type,
               hc.room_type_name,
               hc.room_id,
               hc.guest_name,
               hc.id_type,
               hc.id_number,
               hc.gender,
               hc.birth_date,
               hc.phone_number,
               hc.address,
               hc.checkin_time,
               hc.expected_checkout_time,
               hc.checkout_time,
               hc.stay_days,
               hc.status,
               hc.create_by,
               hc.create_time,
               hc.update_by,
               hc.update_time,
               hc.sms_status
        from hotel_checkin hc
        left join hotel_info hi on hc.hotel_id = hi.id
        JOIN (
            SELECT
                order_no,
                MIN(checkin_time) AS first_checkin_time
            FROM
                hotel_checkin
            WHERE
                status = 2
              AND DATE(checkin_time) = CURDATE()
        GROUP BY
            order_no
            ) first_checkin ON hc.order_no = first_checkin.order_no
        WHERE
            hc.status = 1
          AND hc.sms_status = 1
          AND TIMESTAMPDIFF(MINUTE, first_checkin.first_checkin_time, NOW()) > 60
          AND DATE(first_checkin.first_checkin_time) = CURDATE();
    </select>

    <update id="updateBatchPush" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE hotel_checkin
            SET
            status = #{item.status},
            push_status = #{item.pushStatus},
            push_result = #{item.pushResult}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <update id="updateBatchSMS" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE hotel_checkin
            SET
            sms_status = #{item.smsStatus}
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>