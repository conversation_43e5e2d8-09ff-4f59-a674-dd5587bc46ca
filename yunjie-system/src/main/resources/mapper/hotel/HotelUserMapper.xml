<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelUserMapper">

    <delete id="deleteHotelUserByIds" parameterType="com.yunjie.system.model.request.hotel.user.DeleteHotelUserRequest">
        update hotel_user set del_flag = 2, update_by = #{userName}, update_time = NOW() where user_id in
        <foreach collection="userIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getHotelUserList" parameterType="com.yunjie.system.model.request.hotel.user.GetHotelUserListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelUserResponse">
    select hu.user_id,
           hu.nick_name,
           hu.avatar_url,
           hu.openid,
           hu.union_id,
           hu.mobile,
           hu.bind_flag,
           hu.status,
           hu.del_flag,
           hu.create_by,
           hu.create_time,
           hu.update_by,
           hu.update_time
    from hotel_user hu
    where hu.del_flag = 0
    <if test="openid != null and openid != ''">
        AND hu.openid = #{openid}
    </if>
    <if test="mobile != null and mobile != ''">
        AND hu.mobile = #{mobile}
    </if>
    <if test="status != null and status > -1">
        AND hu.status = #{status}
    </if>
    order by hu.user_id desc
    </select>

</mapper>