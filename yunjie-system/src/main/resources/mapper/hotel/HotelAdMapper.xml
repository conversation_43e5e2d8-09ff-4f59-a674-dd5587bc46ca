<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelAdMapper">

    <delete id="deleteHotelAdByIds" parameterType="com.yunjie.system.model.request.hotel.ad.DeleteHotelAdRequest">
        update hotel_ad set del_flag = 2, update_by = #{userName}, update_time = NOW() where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getHotelAdList" parameterType="com.yunjie.system.model.request.hotel.ad.GetHotelAdListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelAdResponse">
        select ha.id,
        ha.hotel_id,
        hi.hotel_code,
        hi.hotel_name,
        ha.ad_name,
        ha.ad_desc,
        ha.ad_url,
        ha.status,
        ha.del_flag,
        ha.create_by,
        ha.create_time,
        ha.update_by,
        ha.update_time
        from hotel_ad ha
        left join hotel_info hi on ha.hotel_id = hi.id
        where ha.del_flag = 0
        <if test="hotelCode != null and hotelCode != ''">
            AND hi.hotel_code like concat('%', #{hotelCode}, '%')
        </if>
        <if test="hotelName != null and hotelName != ''">
            AND hi.hotel_name like concat('%', #{hotelName}, '%')
        </if>
        <if test="adName != null and adName != ''">
            AND ha.ad_name like concat('%', #{adName}, '%')
        </if>
        <if test="status != null and status > -1">
            AND ha.status = #{status}
        </if>
        order by ha.id desc
    </select>
</mapper>