<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelKnowledgeMapper">

    <delete id="deleteHotelKnowledgeByIds" parameterType="com.yunjie.system.model.request.hotel.knowledge.DeleteHotelKnowledgeRequest">
        update hotel_knowledge set del_flag = 2, update_by = #{userName}, update_time = NOW() where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getHotelKnowledgeList" parameterType="com.yunjie.system.model.request.hotel.knowledge.GetHotelKnowledgeListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelKnowledgeResponse">
        select hk.id,
        hk.hotel_id,
        hi.hotel_code,
        hi.hotel_name,
        hk.knowledge_name,
        hk.knowledge_desc,
        hk.knowledge_type,
        hk.knowledge_content,
        hk.status,
        hk.del_flag,
        hk.create_by,
        hk.create_time,
        hk.update_by,
        hk.update_time
        from hotel_knowledge hk
        left join hotel_info hi on hk.hotel_id = hi.id
        where hk.del_flag = 0
        <if test="hotelCode != null and hotelCode != ''">
            AND hi.hotel_code like concat('%', #{hotelCode}, '%')
        </if>
        <if test="hotelName != null and hotelName != ''">
            AND hi.hotel_name like concat('%', #{hotelName}, '%')
        </if>
        <if test="knowledgeName != null and knowledgeName != ''">
            AND hk.knowledge_name like concat('%', #{knowledgeName}, '%')
        </if>
        <if test="status != null and status > -1">
            AND hk.status = #{status}
        </if>
        order by hk.id desc
    </select>
</mapper>