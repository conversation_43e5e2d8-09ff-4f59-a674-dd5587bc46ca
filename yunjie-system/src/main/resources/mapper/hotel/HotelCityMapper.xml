<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelCityMapper">

    <select id="getHotelCityList" parameterType="com.yunjie.system.model.request.hotel.city.GetHotelCityListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelCityListResponse">
        select id,
        name,
        pid,
        lv
        from hotel_city
        where disabled = 0
        <if test="name != null and name != ''">
            AND name like concat('%', #{name}, '%')
        </if>
        <if test="pid != null and pid > -1">
            AND pid = #{pid}
        </if>
    </select>
</mapper>