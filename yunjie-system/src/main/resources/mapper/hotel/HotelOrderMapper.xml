<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelOrderMapper">

    <select id="getHotelOrderList" parameterType="com.yunjie.system.model.request.hotel.order.GetHotelOrderListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelOrderResponse">
    select ho.id,
           ho.order_no,
           ho.order_flag,
           ho.hotel_id,
           ho.hotel_code,
           hi.hotel_name,
           ho.order_tel,
           ho.order_name,
           ho.order_channel,
           ho.checkin_type,
           ho.checkin_model,
           ho.custom_type,
           ho.hours_rule,
           ho.room_type,
           ho.room_type_name,
           ho.room_num,
           ho.arrive_time,
           ho.days,
           ho.leave_time,
           ho.keep_time,
           ho.order_price,
           ho.out_no,
           ho.seller,
           ho.meno,
           ho.order_status,
           ho.union_no,
           ho.pay_type_code,
           ho.ota_channel,
           ho.last_cancel_time,
           ho.invoice_type,
           ho.is_virtual,
           ho.enable,
           ho.delete_flag,
           ho.created_at,
           ho.create_by,
           ho.updated_at,
           ho.updated_by,
           ho.guest_source
    from hotel_order ho
    left join hotel_info hi on ho.hotel_id = hi.hotel_code
    where ho.delete_flag = 1
    <if test="hotelId != null and hotelId > 0">
        AND ho.hotel_id like concat('%', #{hotelId}, '%')
    </if>
    <if test="hotelCode != null and hotelCode != ''">
        AND ho.hotel_code like concat('%', #{hotelCode}, '%')
    </if>
    <if test="hotelName != null and hotelName != ''">
        AND hi.hotel_name like concat('%', #{hotelName}, '%')
    </if>
    <if test="orderNo != null and orderNo != ''">
        AND ho.order_no = #{orderNo}
    </if>
    <if test="outNo != null and outNo != ''">
        AND ho.out_no = #{outNo}
    </if>
    <if test="orderName != null and orderName != ''">
        AND ho.order_name = #{orderName}
    </if>
    <if test="orderTel != null and orderTel != ''">
        AND ho.order_tel = #{orderTel}
    </if>
    <if test="orderChannel != null and orderChannel > -1">
        AND ho.order_channel = #{orderChannel}
    </if>
    <if test="checkinType != null and checkinType > -1">
        AND ho.checkin_type = #{checkinType}
    </if>
    <if test="checkinModel != null and checkinModel > -1">
        AND ho.checkin_model = #{checkinModel}
    </if>
    <if test="orderStatus != null and orderStatus > -1">
        AND ho.order_status = #{orderStatus}
    </if>
    order by ho.id desc
    </select>

    <update id="updateBatchPush" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE hotel_checkin
            SET
            push_status = #{item.pushStatus},
            push_result = #{item.pushResult}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <update id="updateBatchSMS" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE hotel_order
            SET
            sms_status = #{item.smsStatus}
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>