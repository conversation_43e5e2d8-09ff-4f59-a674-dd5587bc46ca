<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelInterfaceMapper">

    <delete id="deleteHotelInterfaceByIds" parameterType="com.yunjie.system.model.request.hotel.interfaces.DeleteHotelInterfaceRequest">
        update hotel_interface set del_flag = 2, update_by = #{userName}, update_time = NOW() where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getHotelInterfaceList" parameterType="com.yunjie.system.model.request.hotel.interfaces.GetHotelInterfaceListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelInterfaceResponse">
        select hif.id,
        hif.hotel_id,
        hi.hotel_code,
        hi.hotel_name,
        hif.domain_url,
        hif.interface_url,
        hif.interface_desc,
        hif.status,
        hif.del_flag,
        hif.create_by,
        hif.create_time,
        hif.update_by,
        hif.update_time
        from hotel_interface hif
        left join hotel_info hi on hif.hotel_id = hi.id
        where hif.del_flag = 0
        <if test="hotelCode != null and hotelCode != ''">
            AND hi.hotel_code like concat('%', #{hotelCode}, '%')
        </if>
        <if test="hotelName != null and hotelName != ''">
            AND hi.hotel_name like concat('%', #{hotelName}, '%')
        </if>
        <if test="domainUrl != null and domainUrl != ''">
            AND hif.domain_url like concat('%', #{domainUrl}, '%')
        </if>
        <if test="status != null and status > -1">
            AND hif.status = #{status}
        </if>
        order by hif.id desc
    </select>
</mapper>