<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelInfoMapper">

 	<delete id="deleteHotelInfoByIds" parameterType="com.yunjie.system.model.request.hotel.info.DeleteHotelInfoRequest">
 		update hotel_info set del_flag = 2, update_by = #{userName}, update_time = NOW() where id in
 		<foreach collection="ids" item="id" open="(" separator="," close=")">
 			#{id}
        </foreach> 
 	</delete>

	<select id="getHotelInfo" parameterType="com.yunjie.system.model.request.hotel.info.GetHotelInfoRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelInfoResponse">
		select hi.id,
		hi.hotel_code,
		hi.hotel_name,
		hi.hotel_type,
		hi.star_level,
		hi.hotel_pic,
		hi.province_code,
		p.name as provinceName,
		hi.city_code,
		c.name as cityName,
		hi.district_code,
		d.name as districtName,
		hi.detail_address,
		hi.longitude,
		hi.latitude,
		hi.telephone,
		hi.status,
		hi.del_flag,
		hi.create_by,
		hi.create_time,
		hi.update_by,
		hi.update_time
		from hotel_info hi
		left join hotel_city p on hi.province_code = p.id and p.lv = 1
		left join hotel_city c on hi.city_code = c.id and c.lv = 2
		left join hotel_city d on hi.district_code = d.id and d.lv = 3
		where hi.del_flag = 0
		and hi.id = #{id}
	</select>

	<select id="getHotelInfoList" parameterType="com.yunjie.system.model.request.hotel.info.GetHotelInfoListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelInfoResponse">
		select hi.id,
		hi.hotel_code,
		hi.hotel_name,
		hi.hotel_type,
		hi.star_level,
		hi.hotel_pic,
		hi.province_code,
		p.name as provinceName,
		hi.city_code,
		c.name as cityName,
		hi.district_code,
		d.name as districtName,
		hi.detail_address,
		hi.longitude,
		hi.latitude,
		hi.telephone,
		hi.status,
		hi.del_flag,
		hi.create_by,
		hi.create_time,
		hi.update_by,
		hi.update_time
		from hotel_info hi
		left join hotel_city p on hi.province_code = p.id and p.lv = 1
		left join hotel_city c on hi.city_code = c.id and c.lv = 2
		left join hotel_city d on hi.district_code = d.id and d.lv = 3
		where hi.del_flag = 0
		<if test="hotelCode != null and hotelCode != ''">
			AND hi.hotel_code like concat('%', #{hotelCode}, '%')
		</if>
		<if test="hotelName != null and hotelName != ''">
			AND hi.hotel_name like concat('%', #{hotelName}, '%')
		</if>
		<if test="status != null and status > -1">
			AND hi.status = #{status}
		</if>
		order by hi.id desc
	</select>

	<select id="getNearHotelList" parameterType="com.yunjie.system.model.request.hotel.info.GetNearHotelListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelInfoResponse">
		select hi.id,
		hi.hotel_code,
		hi.hotel_name,
		hi.hotel_type,
		hi.star_level,
	    hi.hotel_pic,
		hi.province_code,
		p.name as provinceName,
		hi.city_code,
		c.name as cityName,
		hi.district_code,
		d.name as districtName,
		hi.detail_address,
		hi.longitude,
		hi.latitude,
		hi.telephone,
		(6371000 * acos(
		cos(radians(#{latitude})) * cos(radians(latitude)) *
		cos(radians(longitude) - radians(#{longitude})) +
		sin(radians(#{latitude})) * sin(radians(latitude))
		)) AS distance
		from hotel_info hi
		left join hotel_city p on hi.province_code = p.id and p.lv = 1
		left join hotel_city c on hi.city_code = c.id and c.lv = 2
		left join hotel_city d on hi.district_code = d.id and d.lv = 3
		where hi.del_flag = 0
		AND hi.status = 0
		AND hi.latitude BETWEEN #{latitude} - 0.1 AND #{latitude} + 0.1
		AND hi.longitude BETWEEN #{longitude} - 0.1 AND #{longitude} + 0.1
		HAVING distance <![CDATA[<]]> #{range}
		ORDER BY distance
	</select>
</mapper>