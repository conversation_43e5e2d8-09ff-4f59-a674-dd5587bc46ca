<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelAccountMapper">

    <delete id="deleteHotelAccountByIds" parameterType="com.yunjie.system.model.request.hotel.account.DeleteHotelAccountRequest">
        update hotel_account set del_flag = 2, update_by = #{userName}, update_time = NOW() where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getHotelAccountList" parameterType="com.yunjie.system.model.request.hotel.account.GetHotelAccountListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelAccountResponse">
        select ha.id,
               ha.hotel_id,
               hi.hotel_code,
               hi.hotel_name,
               ha.login_name,
               ha.login_pwd,
               ha.name,
               ha.mobile,
               ha.identity_card,
               ha.status,
               ha.del_flag,
               ha.create_by,
               ha.create_time,
               ha.update_by,
               ha.update_time
        from hotel_account ha
        left join hotel_info hi on ha.hotel_id = hi.id
        where ha.del_flag = 0
        <if test="hotelCode != null and hotelCode != ''">
            AND hi.hotel_code like concat('%', #{hotelCode}, '%')
        </if>
        <if test="hotelName != null and hotelName != ''">
            AND hi.hotel_name like concat('%', #{hotelName}, '%')
        </if>
        <if test="name != null and name != ''">
            AND ha.name like concat('%', #{name}, '%')
        </if>
        <if test="mobile != null and mobile != ''">
            AND ha.mobile = #{mobile}
        </if>
        <if test="status != null and status > -1">
            AND ha.status = #{status}
        </if>
        order by ha.id desc
    </select>
</mapper>