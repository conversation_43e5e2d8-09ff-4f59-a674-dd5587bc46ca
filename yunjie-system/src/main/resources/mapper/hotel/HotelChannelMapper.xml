<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelChannelMapper">

    <delete id="deleteHotelChannelByIds" parameterType="com.yunjie.system.model.request.hotel.channel.DeleteHotelChannelRequest">
        update hotel_channel set del_flag = 2, update_by = #{userName}, update_time = NOW() where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getHotelChannelList" parameterType="com.yunjie.system.model.request.hotel.channel.GetHotelChannelListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelChannelResponse">
    select hc.id,
           hc.hotel_id,
           hi.hotel_code,
           hi.hotel_name,
           hc.channel_type,
           hc.account_id,
           hc.password,
           hc.status,
           hc.del_flag,
           hc.create_by,
           hc.create_time,
           hc.update_by,
           hc.update_time
    from hotel_channel hc
    left join hotel_info hi on hc.hotel_id = hi.id
    where hc.del_flag = 0
    <if test="hotelCode != null and hotelCode != ''">
        AND hi.hotel_code like concat('%', #{hotelCode}, '%')
    </if>
    <if test="hotelName != null and hotelName != ''">
        AND hi.hotel_name like concat('%', #{hotelName}, '%')
    </if>
    <if test="channelType != null and channelType > -1">
        AND hc.channel_type = #{channelType}
    </if>
    <if test="status != null and status > -1">
        AND hc.status = #{status}
    </if>
    order by hc.id desc
    </select>

</mapper>