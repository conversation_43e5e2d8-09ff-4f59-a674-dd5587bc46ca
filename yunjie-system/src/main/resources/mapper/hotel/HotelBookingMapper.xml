<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunjie.system.mapper.hotel.HotelBookingMapper">

    <select id="getHotelBookingList" parameterType="com.yunjie.system.model.request.hotel.booking.GetHotelBookingListRequest" resultType="com.yunjie.system.model.response.hotel.GetHotelBookingResponse">
    select hb.id,
           hb.user_id,
           hb.hotel_id,
           hi.hotel_name,
           hb.room_type_id,
           hb.room_count,
           hb.contact_name,
           hb.contact_phone,
           hb.checkin_date,
           hb.checkout_date,
           hb.arrival_time,
           hb.status,
           hb.remark,
           hb.create_by,
           hb.create_time,
           hb.update_by,
           hb.update_time
    from hotel_booking hb
    left join hotel_info hi on hb.hotel_id = hi.id
    <where>
        <if test="hotelId != null and hotelId > 0">
            AND hb.hotel_id like concat('%', #{hotelId}, '%')
        </if>
        <if test="hotelName != null and hotelName != ''">
            AND hi.hotel_name like concat('%', #{hotelName}, '%')
        </if>
        <if test="userId != null and userId > 0">
            AND hb.user_id = #{userId}
        </if>
        <if test="status != null and status > 0">
            AND hb.status = #{status}
        </if>
    </where>
    order by hb.id desc
    </select>

</mapper>