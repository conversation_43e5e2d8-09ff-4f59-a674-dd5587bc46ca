#!/bin/bash

# Ubuntu服务器Docker一键安装修复脚本
# 使用方法：在Ubuntu服务器上运行 curl -sSL https://raw.githubusercontent.com/your-repo/server-docker-setup.sh | bash
# 或者直接复制此脚本内容到服务器执行

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户或有sudo权限
check_privileges() {
    if [[ $EUID -eq 0 ]]; then
        SUDO=""
    elif sudo -n true 2>/dev/null; then
        SUDO="sudo"
    else
        log_error "需要root权限或sudo权限来安装Docker"
        exit 1
    fi
}

# 检测Ubuntu版本
check_ubuntu_version() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        if [[ "$ID" != "ubuntu" ]]; then
            log_error "此脚本仅适用于Ubuntu系统，检测到: $ID"
            exit 1
        fi
        log_info "检测到Ubuntu版本: $VERSION"
    else
        log_error "无法检测操作系统版本"
        exit 1
    fi
}

# 卸载旧版本Docker
remove_old_docker() {
    log_step "卸载旧版本Docker..."
    $SUDO apt remove -y docker docker-engine docker.io containerd runc 2>/dev/null || true
    log_info "旧版本Docker已卸载"
}

# 更新系统包
update_system() {
    log_step "更新系统包..."
    $SUDO apt update
    log_info "系统包更新完成"
}

# 安装必要的包
install_prerequisites() {
    log_step "安装必要的包..."
    $SUDO apt install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release \
        software-properties-common
    log_info "必要包安装完成"
}

# 添加Docker官方GPG密钥
add_docker_gpg_key() {
    log_step "添加Docker官方GPG密钥..."
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | $SUDO gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    log_info "Docker GPG密钥添加完成"
}

# 添加Docker仓库
add_docker_repository() {
    log_step "添加Docker仓库..."
    echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | $SUDO tee /etc/apt/sources.list.d/docker.list > /dev/null
    $SUDO apt update
    log_info "Docker仓库添加完成"
}

# 安装Docker
install_docker() {
    log_step "安装Docker..."
    $SUDO apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    log_info "Docker安装完成"
}

# 启动Docker服务
start_docker_service() {
    log_step "启动Docker服务..."
    $SUDO systemctl start docker
    $SUDO systemctl enable docker
    log_info "Docker服务已启动并设置为开机自启"
}

# 设置用户权限
setup_user_permissions() {
    log_step "设置用户权限..."
    $SUDO groupadd -f docker
    $SUDO usermod -aG docker $USER
    
    # 临时修改socket权限，避免需要重新登录
    $SUDO chmod 666 /var/run/docker.sock
    log_info "用户权限设置完成"
}

# 安装Docker Compose
install_docker_compose() {
    log_step "安装Docker Compose..."
    
    # 检查是否已经通过插件安装
    if docker compose version &> /dev/null; then
        log_info "Docker Compose (插件版本) 已安装"
        docker compose version
    else
        # 安装独立版本
        log_info "安装Docker Compose独立版本..."
        $SUDO curl -L "https://github.com/docker/compose/releases/download/v2.20.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        $SUDO chmod +x /usr/local/bin/docker-compose
        
        # 创建软链接
        $SUDO ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
        log_info "Docker Compose安装完成"
    fi
}

# 验证安装
verify_installation() {
    log_step "验证Docker安装..."
    
    echo "Docker版本信息:"
    docker --version
    
    echo "Docker Compose版本信息:"
    if command -v docker-compose &> /dev/null; then
        docker-compose --version
    else
        docker compose version
    fi
    
    echo "Docker服务状态:"
    $SUDO systemctl status docker --no-pager -l | head -10
    
    log_step "测试Docker运行..."
    if docker run --rm hello-world &> /dev/null; then
        log_info "✅ Docker测试成功！"
    else
        log_warn "⚠️ Docker测试失败，可能需要重新登录"
    fi
}

# 显示完成信息
show_completion_info() {
    echo ""
    echo "=========================================="
    echo "        Docker安装完成！"
    echo "=========================================="
    echo ""
    echo "安装的组件:"
    echo "  ✅ Docker Engine"
    echo "  ✅ Docker CLI"
    echo "  ✅ Docker Compose"
    echo "  ✅ Containerd"
    echo ""
    echo "服务状态:"
    echo "  ✅ Docker服务已启动"
    echo "  ✅ Docker服务已设置开机自启"
    echo "  ✅ 用户权限已配置"
    echo ""
    echo "如果遇到权限问题，请执行以下命令之一:"
    echo "  1. 注销并重新登录"
    echo "  2. 运行: newgrp docker"
    echo "  3. 临时解决: sudo chmod 666 /var/run/docker.sock"
    echo ""
    echo "现在您可以开始使用Docker了！"
    echo "=========================================="
}

# 主函数
main() {
    echo ""
    echo "=========================================="
    echo "      Ubuntu Docker 一键安装脚本"
    echo "=========================================="
    echo ""
    
    check_privileges
    check_ubuntu_version
    remove_old_docker
    update_system
    install_prerequisites
    add_docker_gpg_key
    add_docker_repository
    install_docker
    start_docker_service
    setup_user_permissions
    install_docker_compose
    verify_installation
    show_completion_info
    
    log_info "安装流程完成！"
}

# 执行主函数
main "$@"
