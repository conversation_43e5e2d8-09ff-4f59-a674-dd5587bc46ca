#!/bin/bash

# 远程服务器Docker问题修复脚本

set -e

# 配置变量
SERVER_IP=""
SERVER_USER="root"
SERVER_PORT="22"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 获取服务器信息
get_server_info() {
    if [ -z "$SERVER_IP" ]; then
        echo -n "请输入服务器IP地址: "
        read SERVER_IP
    fi
    
    if [ -z "$SERVER_USER" ]; then
        echo -n "请输入服务器用户名 [root]: "
        read input_user
        SERVER_USER=${input_user:-root}
    fi
    
    echo -n "请输入SSH端口 [22]: "
    read input_port
    SERVER_PORT=${input_port:-22}
    
    log_info "服务器信息: ${SERVER_USER}@${SERVER_IP}:${SERVER_PORT}"
}

# 测试服务器连接
test_connection() {
    log_step "测试服务器连接..."
    
    if ssh -p ${SERVER_PORT} -o ConnectTimeout=10 ${SERVER_USER}@${SERVER_IP} "echo 'Connection test successful'" > /dev/null 2>&1; then
        log_info "服务器连接成功"
    else
        log_error "无法连接到服务器，请检查IP、端口和认证信息"
        exit 1
    fi
}

# 在远程服务器上修复Docker
fix_docker_on_remote() {
    log_step "在远程服务器上修复Docker问题..."
    
    ssh -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} << 'EOF'
        #!/bin/bash
        
        # 颜色输出
        RED='\033[0;31m'
        GREEN='\033[0;32m'
        YELLOW='\033[1;33m'
        BLUE='\033[0;34m'
        NC='\033[0m'
        
        log_info() {
            echo -e "${GREEN}[INFO]${NC} $1"
        }
        
        log_warn() {
            echo -e "${YELLOW}[WARN]${NC} $1"
        }
        
        log_error() {
            echo -e "${RED}[ERROR]${NC} $1"
        }
        
        log_step() {
            echo -e "${BLUE}[STEP]${NC} $1"
        }
        
        # 检测操作系统
        detect_os() {
            if [ -f /etc/os-release ]; then
                . /etc/os-release
                OS=$NAME
                VER=$VERSION_ID
                log_info "检测到操作系统: $OS $VER"
            else
                log_error "无法检测操作系统"
                exit 1
            fi
        }
        
        # 检查Docker是否安装
        check_docker() {
            if command -v docker &> /dev/null; then
                log_info "Docker已安装: $(docker --version)"
                return 0
            else
                log_warn "Docker未安装"
                return 1
            fi
        }
        
        # 检查Docker服务状态
        check_docker_service() {
            if systemctl is-active --quiet docker; then
                log_info "Docker服务正在运行"
                return 0
            else
                log_warn "Docker服务未运行"
                return 1
            fi
        }
        
        # 安装Docker (Ubuntu/Debian)
        install_docker_ubuntu() {
            log_step "在Ubuntu/Debian上安装Docker..."
            
            # 卸载旧版本
            apt remove -y docker docker-engine docker.io containerd runc 2>/dev/null || true
            
            # 更新包索引
            apt update
            
            # 安装必要的包
            apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
            
            # 添加Docker官方GPG密钥
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            
            # 添加Docker仓库
            echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
            
            # 安装Docker
            apt update
            apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            
            log_info "Docker安装完成"
        }
        
        # 安装Docker (CentOS/RHEL)
        install_docker_centos() {
            log_step "在CentOS/RHEL上安装Docker..."
            
            # 卸载旧版本
            yum remove -y docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine 2>/dev/null || true
            
            # 安装必要的包
            yum install -y yum-utils
            
            # 添加Docker仓库
            yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
            
            # 安装Docker
            yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            
            log_info "Docker安装完成"
        }
        
        # 启动Docker服务
        start_docker() {
            log_step "启动Docker服务..."
            
            # 启动Docker服务
            systemctl start docker
            
            # 设置开机自启
            systemctl enable docker
            
            log_info "Docker服务已启动并设置为开机自启"
        }
        
        # 添加用户到docker组
        setup_docker_permissions() {
            log_step "设置Docker权限..."
            
            # 创建docker组（如果不存在）
            groupadd -f docker
            
            # 添加当前用户到docker组
            usermod -aG docker $USER
            
            # 修改docker.sock权限
            chmod 666 /var/run/docker.sock 2>/dev/null || true
            
            log_info "Docker权限设置完成"
        }
        
        # 安装Docker Compose
        install_docker_compose() {
            log_step "安装Docker Compose..."
            
            if command -v docker-compose &> /dev/null; then
                log_info "Docker Compose已安装: $(docker-compose --version)"
            else
                log_info "安装Docker Compose..."
                
                # 下载Docker Compose
                curl -L "https://github.com/docker/compose/releases/download/v2.20.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
                
                # 设置执行权限
                chmod +x /usr/local/bin/docker-compose
                
                # 创建软链接
                ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
                
                log_info "Docker Compose安装完成: $(docker-compose --version)"
            fi
        }
        
        # 测试Docker
        test_docker() {
            log_step "测试Docker..."
            
            if docker info &> /dev/null; then
                log_info "Docker测试成功"
                docker --version
                docker info | grep "Server Version"
                return 0
            else
                log_error "Docker测试失败"
                return 1
            fi
        }
        
        # 主修复流程
        main_fix() {
            echo "=========================================="
            echo "        远程Docker修复开始"
            echo "=========================================="
            
            detect_os
            
            # 检查Docker安装状态
            if ! check_docker; then
                if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
                    install_docker_ubuntu
                elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]] || [[ "$OS" == *"Rocky"* ]]; then
                    install_docker_centos
                else
                    log_error "不支持的操作系统: $OS"
                    exit 1
                fi
            fi
            
            # 启动Docker服务
            if ! check_docker_service; then
                start_docker
            fi
            
            # 设置权限
            setup_docker_permissions
            
            # 安装Docker Compose
            install_docker_compose
            
            # 测试Docker
            if test_docker; then
                echo ""
                echo "=========================================="
                echo "        Docker修复完成！"
                echo "=========================================="
                echo ""
                echo "Docker版本信息:"
                docker --version
                docker-compose --version
                echo ""
                echo "Docker服务状态:"
                systemctl status docker --no-pager -l
            else
                log_error "Docker修复失败，请检查错误信息"
                exit 1
            fi
        }
        
        # 执行修复
        main_fix
EOF
    
    log_info "远程Docker修复完成"
}

# 验证修复结果
verify_fix() {
    log_step "验证Docker修复结果..."
    
    ssh -p ${SERVER_PORT} ${SERVER_USER}@${SERVER_IP} << 'EOF'
        echo "验证Docker状态..."
        
        # 测试Docker命令
        if docker run --rm hello-world &> /dev/null; then
            echo "✅ Docker运行测试成功"
        else
            echo "❌ Docker运行测试失败"
            exit 1
        fi
        
        # 测试Docker Compose
        if docker-compose --version &> /dev/null; then
            echo "✅ Docker Compose可用"
        else
            echo "❌ Docker Compose不可用"
        fi
        
        echo ""
        echo "当前Docker环境:"
        docker system info | grep -E "(Server Version|Storage Driver|Logging Driver|Cgroup Driver)"
EOF
    
    log_info "验证完成"
}

# 主函数
main() {
    echo ""
    echo "=========================================="
    echo "      远程服务器Docker修复工具"
    echo "=========================================="
    echo ""
    
    get_server_info
    test_connection
    fix_docker_on_remote
    verify_fix
    
    echo ""
    echo "=========================================="
    echo "修复完成！现在可以在远程服务器上使用Docker了"
    echo ""
    echo "接下来您可以："
    echo "1. 运行 ./quick-deploy.sh 进行应用部署"
    echo "2. 或者手动上传文件并执行 ./deploy.sh"
    echo "=========================================="
}

# 执行主函数
main "$@"
