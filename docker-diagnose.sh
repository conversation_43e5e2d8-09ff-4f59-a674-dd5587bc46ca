#!/bin/bash

# Docker问题快速诊断脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

echo ""
echo "=========================================="
echo "        Docker问题快速诊断"
echo "=========================================="
echo ""

# 1. 检查Docker是否安装
log_step "1. 检查Docker安装状态"
if command -v docker &> /dev/null; then
    log_info "✅ Docker已安装"
    docker --version
else
    log_error "❌ Docker未安装"
    echo "   解决方案: 运行 ./fix-docker.sh 安装Docker"
fi

echo ""

# 2. 检查Docker服务状态
log_step "2. 检查Docker服务状态"
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    if pgrep -f "Docker Desktop" > /dev/null; then
        log_info "✅ Docker Desktop正在运行"
    else
        log_error "❌ Docker Desktop未运行"
        echo "   解决方案: 启动Docker Desktop应用"
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    if systemctl is-active --quiet docker 2>/dev/null; then
        log_info "✅ Docker服务正在运行"
    else
        log_error "❌ Docker服务未运行"
        echo "   解决方案: sudo systemctl start docker"
    fi
else
    log_warn "⚠️  无法检测Docker服务状态（未知操作系统）"
fi

echo ""

# 3. 检查用户权限
log_step "3. 检查用户权限"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if groups $USER | grep -q docker; then
        log_info "✅ 用户 $USER 在docker组中"
    else
        log_error "❌ 用户 $USER 不在docker组中"
        echo "   解决方案: sudo usermod -aG docker $USER && newgrp docker"
    fi
else
    log_info "✅ macOS无需检查用户组权限"
fi

echo ""

# 4. 测试Docker连接
log_step "4. 测试Docker连接"
if docker info &> /dev/null; then
    log_info "✅ Docker连接正常"
else
    log_error "❌ 无法连接到Docker守护进程"
    echo "   这是您遇到的错误！"
fi

echo ""

# 5. 检查Docker socket
log_step "5. 检查Docker socket"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if [ -S /var/run/docker.sock ]; then
        log_info "✅ Docker socket存在"
        ls -la /var/run/docker.sock
    else
        log_error "❌ Docker socket不存在"
    fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
    if [ -S /var/run/docker.sock ]; then
        log_info "✅ Docker socket存在"
    else
        log_warn "⚠️  Docker socket不存在（可能是Docker Desktop未启动）"
    fi
fi

echo ""

# 6. 检查Docker Compose
log_step "6. 检查Docker Compose"
if command -v docker-compose &> /dev/null; then
    log_info "✅ Docker Compose已安装"
    docker-compose --version
else
    log_warn "⚠️  Docker Compose未安装"
    echo "   解决方案: 运行 ./fix-docker.sh 安装Docker Compose"
fi

echo ""

# 7. 提供解决方案
log_step "7. 解决方案建议"
echo ""

if ! command -v docker &> /dev/null; then
    echo "🔧 Docker未安装:"
    echo "   运行: ./fix-docker.sh"
    echo ""
fi

if [[ "$OSTYPE" == "darwin"* ]]; then
    if ! pgrep -f "Docker Desktop" > /dev/null; then
        echo "🔧 Docker Desktop未运行:"
        echo "   1. 打开应用程序文件夹"
        echo "   2. 双击Docker Desktop启动"
        echo "   3. 等待Docker启动完成"
        echo ""
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if ! systemctl is-active --quiet docker 2>/dev/null; then
        echo "🔧 Docker服务未启动:"
        echo "   sudo systemctl start docker"
        echo "   sudo systemctl enable docker"
        echo ""
    fi
    
    if ! groups $USER | grep -q docker; then
        echo "🔧 用户权限问题:"
        echo "   sudo usermod -aG docker $USER"
        echo "   newgrp docker"
        echo "   # 或者注销重新登录"
        echo ""
    fi
fi

echo "🔧 通用解决方案:"
echo "   1. 运行自动修复脚本: ./fix-docker.sh"
echo "   2. 重启Docker服务"
echo "   3. 重启系统（如果其他方法无效）"
echo ""

echo "🔧 远程服务器问题:"
echo "   运行: ./remote-docker-fix.sh"
echo ""

echo "=========================================="
echo "诊断完成！请根据上述建议解决问题。"
echo "=========================================="
