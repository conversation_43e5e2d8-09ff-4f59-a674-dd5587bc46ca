package com.yunjie.web.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.utils.JwtUtil;
import com.yunjie.common.utils.bean.BeanUtils;
import com.yunjie.system.model.request.hotel.user.GetHotelUserRequest;
import com.yunjie.system.model.response.hotel.GetHotelUserResponse;
import com.yunjie.system.model.response.hotel.HotelUserResponse;
import com.yunjie.system.service.hotel.HotelUserService;
import com.yunjie.system.service.login.LoginContext;
import com.yunjie.system.service.login.LoginContextHolder;
import com.yunjie.system.service.login.PlatformTypeEnum;
import com.yunjie.web.core.config.IgnoreUrlsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.annotation.Resource;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

/**
 * C端登录验证
 *
 */
@Slf4j
@Order(0)
@WebFilter(filterName = "AuthLoginFilter", urlPatterns = "/api/*")
public class AuthLoginFilter implements Filter {

    @Value("${auth-login.enable}")
    private Boolean authLoginEnable;

    @Resource
    private HotelUserService hotelUserService;

    @Resource
    private IgnoreUrlsConfig ignoreUrlsConfig;

    /**
     * 小程序登录验证
     *
     * @param request
     * @param response
     * @param filterChain
     * @throws IOException
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws IOException {
        R result = R.fail("登录验证失败，请重试!");
        try {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;
            log.info("AuthLoginFilter登录验证,请求地址:{},{}", httpServletRequest.getRequestURI(), httpServletRequest.getContextPath());

            // 如果是OPTIONS，不走后续操作，直接返回
            if (HttpMethod.OPTIONS.toString().equals(httpServletRequest.getMethod())) {
                filterChain.doFilter(httpServletRequest, response);
                return;
            }

            //判断是否跳过登录验证
            if (!authLoginEnable) {
                filterChain.doFilter(httpServletRequest, response);
                return;
            }

            //过滤白名单地址
            PathMatcher pathMatcher = new AntPathMatcher();
            for (String ignoreUrl : ignoreUrlsConfig.authIgnoreLoginUrls()) {
                if (pathMatcher.match(ignoreUrl, httpServletRequest.getRequestURI())) {
                    filterChain.doFilter(httpServletRequest, response);
                    return;
                }
            }

            String token = httpServletRequest.getHeader("token");
            String userId = httpServletRequest.getHeader("userId");
            log.info("AuthLoginFilter登录验证,入参:{},{}", userId, token);
            if (StringUtils.isBlank(userId) || StringUtils.isBlank(token)) {
                result.setMsg("用户未登录！");
                outputJson((HttpServletResponse) response,
                        JSON.toJSONString(result, SerializerFeature.WriteMapNullValue));
                return;
            }

            //过期判断
            if (!JwtUtil.validateToken(token, userId)) {
                result.setCode(-1);
                result.setMsg("登录状态已失效，请重新登录！");
                outputJson((HttpServletResponse) response, JSON.toJSONString(result, SerializerFeature.WriteMapNullValue));
                return;
            }

            //登录用户信息
            final LoginContext loginContext = LoginContextHolder.getLoginContext();

            // 默认小程序
            loginContext.setPlatEnum(PlatformTypeEnum.WEIXIN_APPLET);
            String terminal = httpServletRequest.getHeader("terminal");
            if (PlatformTypeEnum.WEIXIN_APPLET.getPlatformCode().equals(terminal)) {
                loginContext.setPlatEnum(PlatformTypeEnum.H5);
            }

            //判断用户是否存在
            GetHotelUserRequest getHotelUserRequest = new GetHotelUserRequest();
            getHotelUserRequest.setUserId(Long.valueOf(userId));
            GetHotelUserResponse getHotelUserResponse = hotelUserService.getHotelUser(getHotelUserRequest);
            if (getHotelUserResponse == null) {
                result.setMsg("当前用户不存在，请重试！");
                outputJson((HttpServletResponse) response,
                        JSON.toJSONString(result, SerializerFeature.WriteMapNullValue));
                return;
            }

            HotelUserResponse hotelUserResponse = new HotelUserResponse();
            BeanUtils.copyBeanProp(hotelUserResponse, getHotelUserResponse);

            if (!hotelUserResponse.getStatus().equals(0)) {
                result.setMsg("用户状态异常");
                outputJson((HttpServletResponse) response,
                        JSON.toJSONString(result, SerializerFeature.WriteMapNullValue));
                return;
            }

            loginContext.setUserDTO(hotelUserResponse);

            log.info("AuthLoginFilter登录验证,存储登录用户信息成功");
            filterChain.doFilter(httpServletRequest, response);
        } catch (Exception ex) {
            log.error("AuthLoginFilter登录验证异常,异常信息:{}", ex.getMessage(), ex);
            outputJson((HttpServletResponse) response,
                    JSON.toJSONString(result, SerializerFeature.WriteMapNullValue));
        } finally {
            LoginContextHolder.cleanLoginContext();
        }
    }

    /**
     * 返回异常信息
     *
     * @param response
     * @param str
     * @throws IOException
     */
    private void outputJson(HttpServletResponse response, String str) throws IOException {
        //设置返回的Content-typ格式编码
        response.setHeader("content-type", "application/json;charset=UTF-8");
        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(str.getBytes(StandardCharsets.UTF_8));
            outputStream.flush();
        } catch (IOException e) {
            //这里的异常，一般是由于客户端已经关闭了连接，导致无法返回错误报错，使用warn告警即可
            log.error("AuthLoginFilter登录验证返回信息异常:{}" + e.getMessage(), e);
        }
    }
}
