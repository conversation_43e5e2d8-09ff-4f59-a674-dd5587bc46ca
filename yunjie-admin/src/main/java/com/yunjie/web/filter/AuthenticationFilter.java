package com.yunjie.web.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.utils.sign.SignUtil;
import com.yunjie.web.core.config.IgnoreUrlsConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import javax.annotation.Resource;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * C端鉴权
 *
 */
@Slf4j
@Order(0)
@WebFilter(filterName = "AuthenticationFilter", urlPatterns = "/api/*")
public class AuthenticationFilter implements Filter {

    @Resource
    private IgnoreUrlsConfig ignoreUrlsConfig;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Value("${sign.enable}")
    private Boolean authEnable;

    @Value("${sign.appId}")
    private String selfAppId;

    @Value("${sign.appKey}")
    private String selfAppKey;

    @Value("${sign.expireSeconds}")
    private Integer expireSeconds;

    /**
     * ecms登录验证
     *
     * @param request
     * @param response
     * @param filterChain
     * @throws IOException
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws IOException {
        R commonResult = R.fail("网络繁忙，请重试!");
        String url = "";
        try {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;

            // 如果是OPTIONS，不走后续操作，直接返回
            if (HttpMethod.OPTIONS.toString().equals(httpServletRequest.getMethod())) {
                filterChain.doFilter(httpServletRequest, response);
                return;
            }

            //判断是否跳过登录验证
            if (!authEnable) {
                filterChain.doFilter(httpServletRequest, response);
                return;
            }

            //过滤白名单地址
            PathMatcher pathMatcher = new AntPathMatcher();
            for (String ignoreUrl : ignoreUrlsConfig.authIgnoreUrls()) {
                if (pathMatcher.match(ignoreUrl, httpServletRequest.getRequestURI())) {
                    filterChain.doFilter(httpServletRequest, response);
                    return;
                }
            }

            String appId = httpServletRequest.getHeader("appid");
            String nonceStr = httpServletRequest.getHeader("noncestr");
            String timestamp = httpServletRequest.getHeader("timestamp");
            String sign = httpServletRequest.getHeader("sign");
            url = String.format("%s%s,%s", "鉴权请求地址:", httpServletRequest.getRequestURI(), httpServletRequest.getContextPath());
            log.info(String.format("%s%s%s%s,%s,%s,%s", "鉴权地址:", url, "鉴权参数:", appId, nonceStr, timestamp, sign));

            if (StringUtils.isBlank(appId)) {
                List headList = new ArrayList();
                Enumeration<String> enumeration = httpServletRequest.getHeaderNames();
                while (enumeration.hasMoreElements()) {
                    String name = enumeration.nextElement();
                    String value = httpServletRequest.getHeader(name);
                    headList.add(name + "-" + value);
                }
                log.info(String.format("%s%s", "鉴权失败,头部参数:", JSON.toJSONString(headList)));
            }

            if (StringUtils.isBlank(appId) || !appId.equals(selfAppId)) {
                log.info("鉴权失败,appId不匹配,入参:{},配置:{}", appId, selfAppId);
                commonResult.setCode(40001);
                outputJson((HttpServletResponse) response,
                        JSON.toJSONString(commonResult, SerializerFeature.WriteMapNullValue));
                return;
            }

            if (StringUtils.isBlank(nonceStr)) {
                commonResult.setCode(40002);
                log.info("鉴权失败,noncestr为空,入参:{}", nonceStr);
                outputJson((HttpServletResponse) response,
                        JSON.toJSONString(commonResult, SerializerFeature.WriteMapNullValue));
                return;
            } else {
                boolean lock = redisTemplate.opsForValue().setIfAbsent(nonceStr, "1", 5, TimeUnit.MINUTES);
                if (!lock) {
                    commonResult.setCode(40003);
                    log.info("鉴权失败,noncestr重复使用,入参:{}", nonceStr);
                    outputJson((HttpServletResponse) response,
                            JSON.toJSONString(commonResult, SerializerFeature.WriteMapNullValue));
                    return;
                }
            }

            if (StringUtils.isBlank(timestamp)) {
                commonResult.setCode(40004);
                log.info("鉴权失败,timestamp为空,入参:{}", timestamp);
                outputJson((HttpServletResponse) response,
                        JSON.toJSONString(commonResult, SerializerFeature.WriteMapNullValue));
                return;
            } else {
                //是否过期
                if (!SignUtil.isTimestampValid(timestamp, expireSeconds)) {
                    commonResult.setCode(40005);
                    log.info("鉴权失败,timestamp过期,入参:{}", timestamp);
                    outputJson((HttpServletResponse) response,
                            JSON.toJSONString(commonResult, SerializerFeature.WriteMapNullValue));
                    return;
                }
            }

            if (StringUtils.isBlank(sign)) {
                commonResult.setCode(40006);
                log.info("鉴权失败,签名为空,入参:{}", sign);
                outputJson((HttpServletResponse) response,
                        JSON.toJSONString(commonResult, SerializerFeature.WriteMapNullValue));
                return;
            }

            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("appId", appId);
            paramMap.put("nonceStr", nonceStr);
            paramMap.put("timestamp", timestamp);
            paramMap.put("sign", sign);

            // 生成服务端sign签名并比较
            // 签名方式sign=md5(appId=xxx&nonceStr=xxx&timestamp=xxx&appKey=xxx)
            String selfSign = SignUtil.generateSign(paramMap, selfAppKey);
            log.info(String.format("%s%s,%s", "签名验证匹配:", sign, selfSign));
            if (!selfSign.equals(sign)) {
                commonResult.setCode(40007);
                log.info("鉴权失败,签名不匹配,入参:{},生成:{}", sign, selfSign);
                outputJson((HttpServletResponse) response,
                        JSON.toJSONString(commonResult, SerializerFeature.WriteMapNullValue));
                return;
            }
            filterChain.doFilter(httpServletRequest, response);
        } catch (Exception e) {
            log.info("接口鉴权异常,请求地址:{},异常信息:{}", url, e.getMessage());
            log.error(String.format("%s%s%s%s", "接口鉴权异常,请求地址:", url, ",异常信息:", e.getMessage()), e);
            commonResult.setCode(40000);
            outputJson((HttpServletResponse) response,
                    JSON.toJSONString(commonResult, SerializerFeature.WriteMapNullValue));
            return;
        }
    }

    /**
     * 返回异常信息
     *
     * @param response
     * @param str
     * @throws IOException
     */
    private void outputJson(HttpServletResponse response, String str) throws IOException {
        //设置返回的Content-typ格式编码
        response.setHeader("content-type", "application/json;charset=UTF-8");
        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(str.getBytes(StandardCharsets.UTF_8));
            outputStream.flush();
        } catch (IOException e) {
            //这里的异常，一般是由于客户端已经关闭了连接，导致无法返回错误报错，使用warn告警即可
            log.error("接口鉴权异常,异常信息:" + e.getMessage(), e);
        }
    }
}
