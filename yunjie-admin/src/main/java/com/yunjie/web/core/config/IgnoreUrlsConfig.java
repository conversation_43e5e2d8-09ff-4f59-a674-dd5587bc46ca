package com.yunjie.web.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * 白名单
 *
 */
@Component
public class IgnoreUrlsConfig {

    @Value("${auth.exclude.url}")
    private String excludeUrl;

    @Value("${auth.exclude.login-url}")
    private String excludeLoginUrl;

    /**
     * C端鉴权
     *
     * @return
     */
    public List<String> authIgnoreUrls() {
        List<String> urls = new ArrayList<>();
        Collections.addAll(urls, excludeUrl.split(","));
        return urls;
    }

    /**
     * C端登录鉴权
     *
     * @return
     */
    public List<String> authIgnoreLoginUrls() {
        List<String> urls = new ArrayList<>();
        Collections.addAll(urls, excludeLoginUrl.split(","));
        return urls;
    }
}
