package com.yunjie.web.controller.front;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.ad.GetHotelAdListRequest;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingListRequest;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingRequest;
import com.yunjie.system.model.request.hotel.booking.InsertHotelBookingRequest;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinListRequest;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinRequest;
import com.yunjie.system.model.request.hotel.checkin.InsertHotelCheckinRequest;
import com.yunjie.system.model.request.hotel.info.GetNearHotelListRequest;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderListRequest;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderRequest;
import com.yunjie.system.model.request.hotel.user.UpdateHotelUserRequest;
import com.yunjie.system.model.response.hotel.*;
import com.yunjie.system.service.hotel.*;
import com.yunjie.system.service.login.LoginContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "小程序调用-酒店相关操作接口", value = "酒店相关操作接口")
@RestController
@RequestMapping("/api/web/hotel")
public class HotelController extends BaseController {

    @Resource
    private HotelInfoService hotelInfoService;

    @Resource
    private HotelAdService hotelAdService;

    @Resource
    private HotelOrderService hotelOrderService;

    @Resource
    private HotelUserService hotelUserService;

    @Resource
    private HotelBookingService hotelBookingService;

    @Resource
    private HotelCheckinService hotelCheckinService;

    @ApiOperation("获取附近酒店列表")
    @PostMapping("/getNearHotelList")
    public TableDataInfoNew<GetHotelInfoResponse> getNearHotelList(@Validated @RequestBody GetNearHotelListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelInfoResponse> list = hotelInfoService.getNearHotelList(request);
        return getDataTableNew(list);
    }

    @ApiOperation("获取酒店广告信息列表")
    @PostMapping("/getHotelAdList")
    public TableDataInfoNew<GetHotelAdResponse> getHotelAdList(@RequestBody GetHotelAdListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        request.setStatus(0);
        List<GetHotelAdResponse> list = hotelAdService.getHotelAdList(request);
        return getDataTableNew(list);
    }

    @ApiOperation("获取酒店订单信息")
    @PostMapping("/getHotelOrder")
    public R<GetHotelOrderResponse> getHotelOrder(@Validated @RequestBody GetHotelOrderRequest request) {
        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            return R.fail("您还未登录，请先登录");
        }

        request.setMobile(hotelUserResponse.getMobile());
        return R.ok(hotelOrderService.getHotelOrder(request));
    }

    @ApiOperation("获取酒店订单信息列表")
    @PostMapping("/getHotelOrderList")
    public TableDataInfoNew<GetHotelOrderResponse> getHotelOrderList(@RequestBody GetHotelOrderListRequest request)
    {
        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            throw new RuntimeException("您还未登录，请先登录");
        }

        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        request.setOrderTel(hotelUserResponse.getMobile());
        List<GetHotelOrderResponse> list = hotelOrderService.getHotelOrderList(request);
        return getDataTableNew(list);
    }

    @ApiOperation("更新酒店用户信息")
    @PostMapping("/updateHotelUser")
    public R updateHotelUser(@RequestBody UpdateHotelUserRequest request) {
        if (StringUtils.isBlank(request.getAvatarUrl()) && StringUtils.isBlank(request.getNickName()) && StringUtils.isBlank(request.getUnionId())) {
            return R.fail("用户头像、呢称、unionId至少一项不为空");
        }

        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            return R.fail("您还未登录，请先登录");
        }

        request.setUnionId("");
        request.setMobile("");
        request.setUserId(hotelUserResponse.getUserId());

        int rows = hotelUserService.updateHotelUser(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @ApiOperation("酒店预订")
    @PostMapping("/hotelBooking")
    public R<GetHotelBookingResponse> insertHotelBooking(@Validated @RequestBody InsertHotelBookingRequest request) {
        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            return R.fail("您还未登录，请先登录");
        }

        request.setUserId(hotelUserResponse.getUserId());
        return hotelBookingService.insertHotelBooking(request);
    }

    @ApiOperation("获取酒店预订信息")
    @PostMapping("/getHotelBooking")
    public R<GetHotelBookingResponse> getHotelBooking(@Validated @RequestBody GetHotelBookingRequest request) {
        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            return R.fail("您还未登录，请先登录");
        }

        request.setUserId(hotelUserResponse.getUserId());
        return R.ok(hotelBookingService.getHotelBooking(request));
    }

    @ApiOperation("获取酒店预订信息列表")
    @PostMapping("/getHotelBookingList")
    public TableDataInfoNew<GetHotelBookingResponse> getHotelBookingList(@RequestBody GetHotelBookingListRequest request)
    {
        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            throw new RuntimeException("您还未登录，请先登录");
        }

        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        request.setUserId(hotelUserResponse.getUserId());
        List<GetHotelBookingResponse> list = hotelBookingService.getHotelBookingList(request);
        return getDataTableNew(list);
    }

    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @ApiOperation("酒店预入住")
    @PostMapping("/hotelCheckin")
    public R<GetHotelCheckinResponse> insertHotelCheckin(@Validated @RequestBody InsertHotelCheckinRequest request) {
        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            return R.fail("您还未登录，请先登录");
        }

        request.setUserId(hotelUserResponse.getUserId());
        return hotelCheckinService.insertHotelCheckin(request);
    }

    @ApiOperation("获取酒店预入住信息")
    @PostMapping("/getHotelCheckin")
    public R<GetHotelCheckinResponse> getHotelCheckin(@Validated @RequestBody GetHotelCheckinRequest request) {
        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            return R.fail("您还未登录，请先登录");
        }

        request.setUserId(hotelUserResponse.getUserId());
        return R.ok(hotelCheckinService.getHotelCheckin(request));
    }

    @ApiOperation("获取酒店预入住信息列表")
    @PostMapping("/getHotelCheckinList")
    public TableDataInfoNew<GetHotelCheckinResponse> getHotelCheckinList(@RequestBody GetHotelCheckinListRequest request)
    {
        //判断用户是否登录
        HotelUserResponse hotelUserResponse = LoginContextHolder.getLoginContext().getUserDTO();
        if (hotelUserResponse == null) {
            throw new RuntimeException("您还未登录，请先登录");
        }

        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        request.setUserId(hotelUserResponse.getUserId());
        List<GetHotelCheckinResponse> list = hotelCheckinService.getHotelCheckinList(request);
        return getDataTableNew(list);
    }
}
