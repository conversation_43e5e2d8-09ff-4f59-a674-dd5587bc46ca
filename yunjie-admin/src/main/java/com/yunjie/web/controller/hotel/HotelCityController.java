package com.yunjie.web.controller.hotel;

import com.yunjie.common.core.domain.R;
import com.yunjie.system.model.request.hotel.city.GetHotelCityListRequest;
import com.yunjie.system.model.response.hotel.GetHotelCityListResponse;
import com.yunjie.system.service.hotel.HotelCityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店省市区信息管理", value = "酒店省市区信息相关操作接口")
@RestController
@RequestMapping("/hotelCity")
public class HotelCityController {

    @Resource
    private HotelCityService hotelCityService;

    @ApiOperation("获取省市区列表信息")
    @PostMapping("/getHotelCityList")
    public R<List<GetHotelCityListResponse>> getHotelCityList(@RequestBody GetHotelCityListRequest request) {
        if (request == null) {
            return R.fail("查询参数不能为空");
        }
        if (StringUtils.isBlank(request.getName()) && request.getPid() == null) {
            return R.fail("查询参数不能为空");
        }
        if (request.getPid() != null && request.getPid() < 0) {
            return R.fail("行政区划上级ID参数错误");
        }
        return R.ok(hotelCityService.getHotelCityList(request));
    }

    @ApiOperation("递归获取省市区列表信息")
    @PostMapping("/getHotelCityListBySelect")
    public R<List<GetHotelCityListResponse>> getHotelCityListBySelect() {
        return R.ok(hotelCityService.getHotelCityListBySelect());
    }

}
