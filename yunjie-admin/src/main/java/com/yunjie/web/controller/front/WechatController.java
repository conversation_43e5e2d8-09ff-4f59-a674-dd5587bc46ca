package com.yunjie.web.controller.front;

import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.system.model.request.hotel.user.UpdateHotelUserRequest;
import com.yunjie.system.model.request.wechat.AuthPhoneRequest;
import com.yunjie.system.model.request.wechat.GetAccessTokenRequest;
import com.yunjie.system.model.request.wechat.GetUserPhoneRequest;
import com.yunjie.system.model.request.wechat.WechatLoginRequest;
import com.yunjie.system.model.response.hotel.HotelUserResponse;
import com.yunjie.system.model.response.wechat.GetAccessTokenResponse;
import com.yunjie.system.model.response.wechat.GetUserPhoneResponse;
import com.yunjie.system.service.hotel.HotelUserService;
import com.yunjie.system.service.login.LoginContextHolder;
import com.yunjie.system.service.wechat.WechatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "小程序调用-微信小程序相关操作接口", value = "微信小程序相关操作接口")
@RestController
@RequestMapping("/api/web/wx")
public class WechatController extends BaseController {

    @Resource
    private WechatService wechatService;

    @ApiOperation("微信小程序登录")
    @PostMapping("/login")
    public R<HotelUserResponse> wechatLogin(@Validated @RequestBody WechatLoginRequest request) {
        return wechatService.wechatLogin(request);
    }

    @ApiOperation("获取手机号")
    @PostMapping("/phone_info")
    public R<GetUserPhoneResponse> getUserPhone(@Validated @RequestBody GetUserPhoneRequest request) {
        return wechatService.getUserPhone(request);
    }

    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @ApiOperation("授权手机号注册")
    @PostMapping("/register")
    public R<HotelUserResponse> authPhoneRegister(@Validated @RequestBody AuthPhoneRequest request) {
        return wechatService.authPhoneRegister(request);
    }

    @ApiOperation("获取access_token")
    @PostMapping("/getAccessToken")
    public R<GetAccessTokenResponse> getAccessToken(@Validated @RequestBody GetAccessTokenRequest request) {
        return wechatService.getAccessToken(request);
    }

}
