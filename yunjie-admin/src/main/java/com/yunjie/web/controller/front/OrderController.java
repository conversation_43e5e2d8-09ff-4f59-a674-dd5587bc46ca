package com.yunjie.web.controller.front;

import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.domain.R;
import com.yunjie.system.model.request.hotel.order.InsertHotelOrderRequest;
import com.yunjie.system.model.request.hotel.order.UpdateHotelOrderRequest;
import com.yunjie.system.service.hotel.HotelOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "小程序调用-酒店订单相关操作接口", value = "酒店订单相关操作接口")
@RestController
@RequestMapping("/api/web/hotel/order")
public class OrderController {

    @Resource
    private HotelOrderService hotelOrderService;

    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @ApiOperation("新增酒店订单")
    @PostMapping("/add")
    public R add(@Validated @RequestBody InsertHotelOrderRequest request) {
        return hotelOrderService.insertHotelOrder(request);
    }

    @ApiOperation("更新酒店订单")
    @PostMapping("/update")
    public R update(@Validated @RequestBody UpdateHotelOrderRequest request) {
        return hotelOrderService.updateHotelOrder(request);
    }
}
