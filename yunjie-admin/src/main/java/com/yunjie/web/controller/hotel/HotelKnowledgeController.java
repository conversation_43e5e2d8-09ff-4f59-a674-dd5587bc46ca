package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.knowledge.*;
import com.yunjie.system.model.response.hotel.GetHotelKnowledgeResponse;
import com.yunjie.system.service.hotel.HotelKnowledgeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店知识库信息管理", value = "酒店知识库信息相关操作接口")
@RestController
@RequestMapping("/hotelKnowledge")
public class HotelKnowledgeController extends BaseController {

    @Resource
    private HotelKnowledgeService hotelKnowledgeService;

    @RepeatSubmit(interval = 5000, message = "请求过于频繁")
    @ApiOperation("新增酒店知识库信息")
    @PostMapping("/add")
    public R add(@Validated @RequestBody InsertHotelKnowledgeRequest request) {
        int rows = hotelKnowledgeService.insertHotelKnowledge(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("更新酒店知识库信息")
    @PostMapping("/update")
    public R update(@Validated @RequestBody UpdateHotelKnowledgeRequest request) {
        int rows = hotelKnowledgeService.updateHotelKnowledge(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("变更酒店知识库信息状态")
    @PostMapping("/changeStatus")
    public R changeStatus(@Validated @RequestBody ChangeHotelKnowledgeStatusRequest request) {
        int rows = hotelKnowledgeService.changeStatus(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("删除酒店知识库信息")
    @PostMapping("/delete")
    public R delete(@Validated @RequestBody DeleteHotelKnowledgeRequest request) {
        if (request == null || request.getIds() == null || request.getIds().length == 0) {
            return R.fail("主键ID不能为空");
        }

        int rows = hotelKnowledgeService.deleteHotelKnowledge(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("获取酒店知识库信息")
    @PostMapping("/getHotelKnowledge")
    public R<GetHotelKnowledgeResponse> getHotelKnowledge(@Validated @RequestBody GetHotelKnowledgeRequest request) {
        return R.ok(hotelKnowledgeService.getHotelKnowledge(request));
    }

    @ApiOperation("获取酒店知识库信息列表")
    @PostMapping("/getHotelKnowledgeList")
    public TableDataInfoNew<GetHotelKnowledgeResponse> getHotelKnowledgeList(@RequestBody GetHotelKnowledgeListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelKnowledgeResponse> list = hotelKnowledgeService.getHotelKnowledgeList(request);
        return getDataTableNew(list);
    }
}
