package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinListRequest;
import com.yunjie.system.model.request.hotel.checkin.GetHotelCheckinRequest;
import com.yunjie.system.model.response.hotel.GetHotelCheckinResponse;
import com.yunjie.system.service.hotel.HotelCheckinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店入住信息管理", value = "酒店入住信息相关操作接口")
@RestController
@RequestMapping("/hotelCheckin")
public class HotelCheckinController extends BaseController {

    @Resource
    private HotelCheckinService hotelCheckinService;

    @ApiOperation("获取酒店入住信息")
    @PostMapping("/getHotelCheckin")
    public R<GetHotelCheckinResponse> getHotelCheckin(@Validated @RequestBody GetHotelCheckinRequest request) {
        return R.ok(hotelCheckinService.getHotelCheckin(request));
    }

    @ApiOperation("获取酒店入住信息列表")
    @PostMapping("/getHotelCheckinList")
    public TableDataInfoNew<GetHotelCheckinResponse> getHotelCheckinList(@RequestBody GetHotelCheckinListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelCheckinResponse> list = hotelCheckinService.getHotelCheckinList(request);
        return getDataTableNew(list);
    }
}
