package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.user.ChangeHotelUserStatusRequest;
import com.yunjie.system.model.request.hotel.user.DeleteHotelUserRequest;
import com.yunjie.system.model.request.hotel.user.GetHotelUserListRequest;
import com.yunjie.system.model.request.hotel.user.GetHotelUserRequest;
import com.yunjie.system.model.response.hotel.GetHotelUserResponse;
import com.yunjie.system.service.hotel.HotelUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店用户信息管理", value = "酒店用户信息相关操作接口")
@RestController
@RequestMapping("/hotelUser")
public class HotelUserController extends BaseController {

    @Resource
    private HotelUserService hotelUserService;

    @ApiOperation("变更酒店用户信息状态")
    @PostMapping("/changeStatus")
    public R changeState(@Validated @RequestBody ChangeHotelUserStatusRequest request) {
        int rows = hotelUserService.changeStatus(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("删除酒店用户信息")
    @PostMapping("/delete")
    public R delete(@Validated @RequestBody DeleteHotelUserRequest request) {
        if (request == null || request.getUserIds() == null || request.getUserIds().length == 0) {
            return R.fail("用户ID不能为空");
        }

        int rows = hotelUserService.deleteHotelUser(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("获取酒店用户信息")
    @PostMapping("/getHotelUser")
    public R<GetHotelUserResponse> getHotelUser(@Validated @RequestBody GetHotelUserRequest request) {
        return R.ok(hotelUserService.getHotelUser(request));
    }

    @ApiOperation("获取酒店用户信息列表")
    @PostMapping("/getHotelUserList")
    public TableDataInfoNew<GetHotelUserResponse> getHotelUserList(@RequestBody GetHotelUserListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelUserResponse> list = hotelUserService.getHotelUserList(request);
        return getDataTableNew(list);
    }
}
