package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.account.*;
import com.yunjie.system.model.response.hotel.GetHotelAccountResponse;
import com.yunjie.system.service.hotel.HotelAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店账号信息管理", value = "酒店账号信息相关操作接口")
@RestController
@RequestMapping("/hotelAccount")
public class HotelAccountController extends BaseController {

    @Resource
    private HotelAccountService hotelAccountService;

    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @ApiOperation("新增酒店账号信息")
    @PostMapping("/add")
    public R add(@Validated @RequestBody InsertHotelAccountRequest request) {
        int rows = hotelAccountService.insertHotelAccount(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("更新酒店账号信息")
    @PostMapping("/update")
    public R update(@Validated @RequestBody UpdateHotelAccountRequest request) {
        int rows = hotelAccountService.updateHotelAccount(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("变更酒店账号信息状态")
    @PostMapping("/changeStatus")
    public R changeStatus(@Validated @RequestBody ChangeHotelAccountStatusRequest request) {
        int rows = hotelAccountService.changeStatus(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("删除酒店账号信息")
    @PostMapping("/delete")
    public R delete(@Validated @RequestBody DeleteHotelAccountRequest request) {
        if (request == null || request.getIds() == null || request.getIds().length == 0) {
            return R.fail("主键ID不能为空");
        }

        int rows = hotelAccountService.deleteHotelAccount(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("获取酒店账号信息")
    @PostMapping("/getHotelAccount")
    public R<GetHotelAccountResponse> getHotelAccount(@Validated @RequestBody GetHotelAccountRequest request) {
        return R.ok(hotelAccountService.getHotelAccount(request));
    }

    @ApiOperation("获取酒店账号信息列表")
    @PostMapping("/getHotelAccountList")
    public TableDataInfoNew<GetHotelAccountResponse> getHotelAccountList(@RequestBody GetHotelAccountListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelAccountResponse> list = hotelAccountService.getHotelAccountList(request);
        return getDataTableNew(list);
    }
}
