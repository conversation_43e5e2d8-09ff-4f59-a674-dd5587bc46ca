package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.ad.*;
import com.yunjie.system.model.response.hotel.GetHotelAdResponse;
import com.yunjie.system.service.hotel.HotelAdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店广告信息管理", value = "酒店广告信息相关操作接口")
@RestController
@RequestMapping("/hotelAd")
public class HotelAdController extends BaseController {

    @Resource
    private HotelAdService hotelAdService;

    @RepeatSubmit(interval = 5000, message = "请求过于频繁")
    @ApiOperation("新增酒店广告信息")
    @PostMapping("/add")
    public R add(@Validated @RequestBody InsertHotelAdRequest request) {
        int rows = hotelAdService.insertHotelAd(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("更新酒店广告信息")
    @PostMapping("/update")
    public R update(@Validated @RequestBody UpdateHotelAdRequest request) {
        int rows = hotelAdService.updateHotelAd(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("变更酒店广告信息状态")
    @PostMapping("/changeStatus")
    public R changeStatus(@Validated @RequestBody ChangeHotelAdStatusRequest request) {
        int rows = hotelAdService.changeStatus(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("删除酒店广告信息")
    @PostMapping("/delete")
    public R delete(@Validated @RequestBody DeleteHotelAdRequest request) {
        if (request == null || request.getIds() == null || request.getIds().length == 0) {
            return R.fail("主键ID不能为空");
        }

        int rows = hotelAdService.deleteHotelAd(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("获取酒店广告信息")
    @PostMapping("/getHotelAd")
    public R<GetHotelAdResponse> getHotelAd(@Validated @RequestBody GetHotelAdRequest request) {
        return R.ok(hotelAdService.getHotelAd(request));
    }

    @ApiOperation("获取酒店广告信息列表")
    @PostMapping("/getHotelAdList")
    public TableDataInfoNew<GetHotelAdResponse> getHotelAdList(@RequestBody GetHotelAdListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelAdResponse> list = hotelAdService.getHotelAdList(request);
        return getDataTableNew(list);
    }
}
