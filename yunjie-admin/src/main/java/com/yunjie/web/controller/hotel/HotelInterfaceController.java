package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.interfaces.*;
import com.yunjie.system.model.response.hotel.GetHotelInterfaceResponse;
import com.yunjie.system.service.hotel.HotelInterfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店PMS对接地址信息管理", value = "酒店PMS对接地址信息相关操作接口")
@RestController
@RequestMapping("/hotelInterface")
public class HotelInterfaceController extends BaseController {

    @Resource
    private HotelInterfaceService hotelInterfaceService;

    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @ApiOperation("新增酒店PMS对接地址信息")
    @PostMapping("/add")
    public R add(@Validated @RequestBody InsertHotelInterfaceRequest request) {
        int rows = hotelInterfaceService.insertHotelInterface(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("更新酒店PMS对接地址信息")
    @PostMapping("/update")
    public R update(@Validated @RequestBody UpdateHotelInterfaceRequest request) {
        int rows = hotelInterfaceService.updateHotelInterface(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("变更酒店PMS对接地址信息状态")
    @PostMapping("/changeStatus")
    public R changeStatus(@Validated @RequestBody ChangeHotelInterfaceStatusRequest request) {
        int rows = hotelInterfaceService.changeStatus(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("删除酒店PMS对接地址信息")
    @PostMapping("/delete")
    public R delete(@Validated @RequestBody DeleteHotelInterfaceRequest request) {
        if (request == null || request.getIds() == null || request.getIds().length == 0) {
            return R.fail("主键ID不能为空");
        }

        int rows = hotelInterfaceService.deleteHotelInterface(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("获取酒店PMS对接地址信息")
    @PostMapping("/getHotelInterface")
    public R<GetHotelInterfaceResponse> getHotelInterface(@Validated @RequestBody GetHotelInterfaceRequest request) {
        return R.ok(hotelInterfaceService.getHotelInterface(request));
    }

    @ApiOperation("获取酒店PMS对接地址信息列表")
    @PostMapping("/getHotelInterfaceList")
    public TableDataInfoNew<GetHotelInterfaceResponse> getHotelInterfaceList(@RequestBody GetHotelInterfaceListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelInterfaceResponse> list = hotelInterfaceService.getHotelInterfaceList(request);
        return getDataTableNew(list);
    }
}
