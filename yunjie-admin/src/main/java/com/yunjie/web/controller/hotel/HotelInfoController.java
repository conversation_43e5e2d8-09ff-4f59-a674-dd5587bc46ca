package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.info.*;
import com.yunjie.system.model.response.hotel.GetHotelInfoResponse;
import com.yunjie.system.service.hotel.HotelInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店基本信息管理", value = "酒店基本信息相关操作接口")
@RestController
@RequestMapping("/hotelInfo")
public class HotelInfoController extends BaseController {

    @Resource
    private HotelInfoService hotelInfoService;

    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @ApiOperation("新增酒店基本信息")
    @PostMapping("/add")
    public R add(@Validated @RequestBody InsertHotelInfoRequest request) {
        int rows = hotelInfoService.insertHotelInfo(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("更新酒店基本信息")
    @PostMapping("/update")
    public R update(@Validated @RequestBody UpdateHotelInfoRequest request) {
        int rows = hotelInfoService.updateHotelInfo(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("变更酒店基本信息状态")
    @PostMapping("/changeStatus")
    public R changeState(@Validated @RequestBody ChangeHotelInfoStatusRequest request) {
        int rows = hotelInfoService.changeStatus(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("删除酒店基本信息")
    @PostMapping("/delete")
    public R delete(@Validated @RequestBody DeleteHotelInfoRequest request) {
        if (request == null || request.getIds() == null || request.getIds().length == 0) {
            return R.fail("主键ID不能为空");
        }

        int rows = hotelInfoService.deleteHotelInfo(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("获取酒店基本信息")
    @PostMapping("/getHotelInfo")
    public R<GetHotelInfoResponse> getHotelInfo(@Validated @RequestBody GetHotelInfoRequest request) {
        return R.ok(hotelInfoService.getHotelInfo(request));
    }

    @ApiOperation("获取酒店基本信息列表")
    @PostMapping("/getHotelInfoList")
    public TableDataInfoNew<GetHotelInfoResponse> getHotelInfoList(@RequestBody GetHotelInfoListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelInfoResponse> list = hotelInfoService.getHotelInfoList(request);
        return getDataTableNew(list);
    }
}
