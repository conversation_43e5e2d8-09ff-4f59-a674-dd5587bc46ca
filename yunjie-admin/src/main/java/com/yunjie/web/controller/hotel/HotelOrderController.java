package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderListRequest;
import com.yunjie.system.model.request.hotel.order.GetHotelOrderRequest;
import com.yunjie.system.model.response.hotel.GetHotelOrderResponse;
import com.yunjie.system.service.hotel.HotelOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店订单信息管理", value = "酒店订单信息相关操作接口")
@RestController
@RequestMapping("/hotelOrder")
public class HotelOrderController extends BaseController {

    @Resource
    private HotelOrderService hotelOrderService;

    @ApiOperation("获取酒店订单信息")
    @PostMapping("/getHotelOrder")
    public R<GetHotelOrderResponse> getHotelOrder(@Validated @RequestBody GetHotelOrderRequest request) {
        return R.ok(hotelOrderService.getHotelOrder(request));
    }

    @ApiOperation("获取酒店订单信息列表")
    @PostMapping("/getHotelOrderList")
    public TableDataInfoNew<GetHotelOrderResponse> getHotelOrderList(@RequestBody GetHotelOrderListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelOrderResponse> list = hotelOrderService.getHotelOrderList(request);
        return getDataTableNew(list);
    }
}
