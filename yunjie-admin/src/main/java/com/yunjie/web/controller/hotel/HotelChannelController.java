package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.annotation.RepeatSubmit;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.channel.*;
import com.yunjie.system.model.response.hotel.GetHotelChannelResponse;
import com.yunjie.system.service.hotel.HotelChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店渠道信息管理", value = "酒店渠道信息相关操作接口")
@RestController
@RequestMapping("/hotelChannel")
public class HotelChannelController extends BaseController {

    @Resource
    private HotelChannelService hotelChannelService;

    @RepeatSubmit(interval = 1000, message = "请求过于频繁")
    @ApiOperation("新增酒店渠道信息")
    @PostMapping("/add")
    public R add(@Validated @RequestBody InsertHotelChannelRequest request) {
        int rows = hotelChannelService.insertHotelChannel(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("更新酒店渠道信息")
    @PostMapping("/update")
    public R update(@Validated @RequestBody UpdateHotelChannelRequest request) {
        int rows = hotelChannelService.updateHotelChannel(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("变更酒店渠道信息状态")
    @PostMapping("/changeStatus")
    public R changeStatus(@Validated @RequestBody ChangeHotelChannelStatusRequest request) {
        int rows = hotelChannelService.changeStatus(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("删除酒店渠道信息")
    @PostMapping("/delete")
    public R delete(@Validated @RequestBody DeleteHotelChannelRequest request) {
        if (request == null || request.getIds() == null || request.getIds().length == 0) {
            return R.fail("主键ID不能为空");
        }

        int rows = hotelChannelService.deleteHotelChannel(request);
        if (rows > 0) {
            return R.ok();
        }
        return R.fail();
    }

    @ApiOperation("获取酒店渠道信息")
    @PostMapping("/getHotelChannel")
    public R<GetHotelChannelResponse> getHotelChannel(@Validated @RequestBody GetHotelChannelRequest request) {
        return R.ok(hotelChannelService.getHotelChannel(request));
    }

    @ApiOperation("获取酒店渠道信息列表")
    @PostMapping("/getHotelChannelList")
    public TableDataInfoNew<GetHotelChannelResponse> getHotelChannelList(@RequestBody GetHotelChannelListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelChannelResponse> list = hotelChannelService.getHotelChannelList(request);
        return getDataTableNew(list);
    }
}
