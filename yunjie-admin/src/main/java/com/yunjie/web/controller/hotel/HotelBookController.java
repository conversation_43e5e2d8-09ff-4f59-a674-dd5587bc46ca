package com.yunjie.web.controller.hotel;

import com.github.pagehelper.PageHelper;
import com.yunjie.common.core.controller.BaseController;
import com.yunjie.common.core.domain.R;
import com.yunjie.common.core.page.TableDataInfoNew;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingListRequest;
import com.yunjie.system.model.request.hotel.booking.GetHotelBookingRequest;
import com.yunjie.system.model.response.hotel.GetHotelBookingResponse;
import com.yunjie.system.service.hotel.HotelBookingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "酒店预订信息管理", value = "酒店预订信息相关操作接口")
@RestController
@RequestMapping("/hotelBooking")
public class HotelBookController extends BaseController {

    @Resource
    private HotelBookingService hotelBookingService;

    @ApiOperation("获取酒店预订信息")
    @PostMapping("/getHotelBooking")
    public R<GetHotelBookingResponse> getHotelBooking(@Validated @RequestBody GetHotelBookingRequest request) {
        return R.ok(hotelBookingService.getHotelBooking(request));
    }

    @ApiOperation("获取酒店预订信息列表")
    @PostMapping("/getHotelBookingList")
    public TableDataInfoNew<GetHotelBookingResponse> getHotelBookingList(@RequestBody GetHotelBookingListRequest request)
    {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<GetHotelBookingResponse> list = hotelBookingService.getHotelBookingList(request);
        return getDataTableNew(list);
    }
}
