package com.yunjie.web.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yunjie.system.service.hotel.HotelCheckinService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CheckinJobHandler {

    @Resource
    private HotelCheckinService hotelCheckinService;

    /**
     * 推送预入住信息
     **/
    @XxlJob("pushCheckin")
    public void pushCheckin() {
        hotelCheckinService.pushCheckin();
    }

    /**
     * 预入住信息短信提醒
     **/
    @XxlJob("sendSmsCheckin")
    public void sendSmsCheckin() {
        hotelCheckinService.sendSmsCheckin();
    }

}
