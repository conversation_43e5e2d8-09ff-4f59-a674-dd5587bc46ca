package com.yunjie.web.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yunjie.system.service.hotel.HotelOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class OrderJobHandler {

    @Resource
    private HotelOrderService hotelOrderService;

    /**
     * 推送订单
     **/
    @XxlJob("pushOrder")
    public void pushOrder() {
        hotelOrderService.pushOrder();
    }

    /**
     * 预订单短信提醒
     **/
    @XxlJob("sendSmsOrder")
    public void sendSmsOrder() {
        hotelOrderService.sendSmsOrder();
    }
}
