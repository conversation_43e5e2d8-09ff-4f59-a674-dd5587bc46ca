package com.yunjie.common.utils.sign;

import org.springframework.util.DigestUtils;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

public class SignUtil {

    // 生成签名
    public static String generateSign(Map<String, String> params, String secretKey) {
        SortedMap<String, String> sortedParams = new TreeMap<>(params);
        StringBuilder sb = new StringBuilder();

        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if ("sign".equals(entry.getKey()) || entry.getValue() == null) continue;
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        sb.append("appKey=").append(secretKey);

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes()).toUpperCase(); // 可替换为 SHA256
    }

    // 校验时间戳有效性
    public static boolean isTimestampValid(String timestampStr, int expirySeconds) {
        try {
            long ts = Long.parseLong(timestampStr);
            long now = System.currentTimeMillis() / 1000;
            return Math.abs(now - ts) <= expirySeconds;
        } catch (Exception e) {
            return false;
        }
    }
}
