package com.yunjie.common.utils.http;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Map;

@Slf4j
public class HttpUtil {

    static CloseableHttpClient httpClient;

    @PostConstruct
    void init() {
        //设置最大连接数 和 每个路由的最大连接数
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager();
        manager.setMaxTotal(500);
        manager.setDefaultMaxPerRoute(100);
        ConnectionKeepAliveStrategy connectionKeepAliveStrategy = (httpResponse, httpContext) -> {
            return 20 * 1000; // tomcat默认keepAliveTimeout为20s
        };
        httpClient = HttpClients.custom()
                //设置超时时间
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setSocketTimeout(5 * 1000) //与服务器建立连接获取响应数据的超时时间
                        .setConnectTimeout(5 * 1000) //与服务器建立连接的超时时间
                        .setConnectionRequestTimeout(5 * 1000) //从连接池中获取连接的超时时间
                        .build())
                .setConnectionManager(manager)
                .setKeepAliveStrategy(connectionKeepAliveStrategy)
                .build();
    }

    /*
    Get请求
    */
    public static String doGet(String url, Map<String, String> map, Map<String, String> mapHead) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        //参数
        if (map != null && map.size() > 0) {
            String param = "?";
            for (String key : map.keySet()) {
                param += key + "=" + map.get(key).toString() + "&";
            }
            param = param.substring(0, param.length() - 1);
            url += param;
        }
        //创建Get请求
        HttpGet httpGet = new HttpGet(url);
        if (mapHead != null) {
            for (String key : mapHead.keySet()
            ) {
                String vlaue = mapHead.get(key).toString();
                httpGet.setHeader(key, vlaue);
            }
        }
        try {
            //发送请求
            CloseableHttpResponse response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
            log.error(String.format("%s%s", url, "请求失败"), e);
        }
        return null;
    }

    /*
    POST请求
     */
    public static String httpPost(String url, Map mapParam, Map<String, String> mapHead) {
        // 返回body
        String body = null;
        // 获取连接客户端工具
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        // 2、创建一个HttpPost请求
        HttpPost post = new HttpPost(url);
        // 5、设置header信息 通用属性
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        /*业务参数*/
        if (mapHead != null) {
            for (String key : mapHead.keySet()
            ) {
                String vlaue = mapHead.get(key).toString();
                post.setHeader(key, vlaue);
            }
        }
        // 设置参数
        if (mapParam != null) {
            try {
                StringEntity stringEntity = new StringEntity(JSON.toJSONString(mapParam), "UTF-8");
                stringEntity.setContentEncoding("UTF-8");
                stringEntity.setContentType("application/json");
                post.setEntity(stringEntity);
                // 7、执行post请求操作，并拿到结果
                httpResponse = httpClient.execute(post);
                // 获取结果实体
                HttpEntity entity = httpResponse.getEntity();
                if (entity != null) {
                    // 按指定编码转换结果实体为String类型
                    body = EntityUtils.toString(entity, "UTF-8");
                }
                try {
                    httpResponse.close();
                    httpClient.close();
                } catch (IOException e) {
                    log.error(String.format("%s%s", url, "请求失败"), e);
                    e.printStackTrace();
                }
            } catch (Exception e) {
                log.error(String.format("%s%s", url, "请求失败"), e);
                e.printStackTrace();
            }
        }
        return body;
    }

    /**
     * POST请求
     */
    public static String httpPost(String url, String requestBody, Map<String, String> mapHead) {
        // 返回body
        String body = null;
        // 获取连接客户端工具
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        // 2、创建一个HttpPost请求
        HttpPost post = new HttpPost(url);
        // 5、设置header信息 通用属性
        post.setHeader("Content-Type", "application/json;charset=UTF-8");
        /*业务参数*/
        if (mapHead != null) {
            for (String key : mapHead.keySet()) {
                String vlaue = mapHead.get(key);
                post.setHeader(key, vlaue);
            }
        }
        // 设置参数
        if (requestBody != null) {
            try {
                StringEntity stringEntity = new StringEntity(requestBody, "UTF-8");
                stringEntity.setContentEncoding("UTF-8");
                stringEntity.setContentType("application/json");
                post.setEntity(stringEntity);
                // 7、执行post请求操作，并拿到结果
                httpResponse = httpClient.execute(post);
                // 获取结果实体
                HttpEntity entity = httpResponse.getEntity();
                if (entity != null) {
                    // 按指定编码转换结果实体为String类型
                    body = EntityUtils.toString(entity, "UTF-8");
                }
                try {
                    httpResponse.close();
                    httpClient.close();
                } catch (IOException e) {
                    log.error(String.format("%s%s", url, "请求失败"), e);
                    e.printStackTrace();
                }
            } catch (Exception e) {
                log.error(String.format("%s%s", url, "请求失败"), e);
                e.printStackTrace();
            }
        }
        return body;
    }
}
