package com.yunjie.common.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * jwt 用于登录验证
 *
 */
@Slf4j
public class JwtUtil {

    //有效期
    private static final Long JWT_TTL = 30 * 24 * 60 * 60 * 1000L;//30天
    //秘钥
    private static final String ACCESS_KEY = "770A657C00474F108D2E561246FCE71E";
    private static final String CLAIM_KEY_USERNAME = "sub";
    private static final String CLAIM_KEY_CREATED = "created";

    /**
     * 从token中获取用户名
     *
     * @param token
     * @return
     */
    public static String getUserNameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        if (claims == null) {
            return "";
        }
        return claims.getSubject();
    }

    /**
     * 根据用户信息生成token
     *
     * @param userId
     * @return
     */
    public static String generateToken(String userId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_USERNAME, userId);
        claims.put(CLAIM_KEY_CREATED, new Date());
        return generateToken(claims);
    }


    /**
     * token是否有效
     *
     * @param token
     * @param userId
     * @return
     */
    public static boolean validateToken(String token, String userId) {
        String username = getUserNameFromToken(token);
        return userId.equals(username) && !isExpired(token);
    }


    /**
     * uuid生成
     *
     * @return
     */
    private static String getUUID() {
        String accessToken = UUID.randomUUID().toString().replaceAll("-", "");
        return accessToken;
    }

    /**
     * 生成过期时间
     *
     * @return
     */
    private static Date generateExpirationDate() {
        return new Date(System.currentTimeMillis() + JWT_TTL);
    }

    /**
     * 生成jwt
     *
     * @param claims
     * @return
     */
    private static String generateToken(Map<String, Object> claims) {
        return Jwts.builder()
                .setId(getUUID())//唯一ID
                .setClaims(claims)
                .setExpiration(generateExpirationDate())
                .signWith(SignatureAlgorithm.HS512, ACCESS_KEY)//使用Hs256对称加密算法,secretKey为秘钥
                .compact();
    }

    /**
     * 从token中获取JWT中的负载
     *
     * @param token
     * @return
     */
    private static Claims getClaimsFromToken(String token) {
        Claims claims = null;
        try {
            claims = Jwts.parser()
                    .setSigningKey(ACCESS_KEY)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("TOKEN解析异常,错误信息:{}", e.getMessage(), e);
        }
        return claims;
    }

    /**
     * 获取过期时间
     *
     * @param token
     * @return
     */
    private static boolean isExpired(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration().before(new Date());
    }
}
