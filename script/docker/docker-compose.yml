version: '3'

services:
  mysql:
    image: mysql:8.0.31
    container_name: mysql
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      # root 密码
      MYSQL_ROOT_PASSWORD: root
      # 初始化数据库(后续的初始化sql会在这个库执行)
      MYSQL_DATABASE: ry-vue
    ports:
      - "3307:3307"
    volumes:
      # 数据挂载
      - /data/docker/mysql/data/:/var/lib/mysql/
      # 配置挂载
      - /data/docker/mysql/conf/:/etc/mysql/conf.d/
    command:
      # 将mysql8.0默认密码策略 修改为 原先 策略 (mysql8.0对其默认策略做了更改 会导致密码无法匹配)
      --port=3307
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
    privileged: true
    network_mode: "host"

  nginx-web:
    image: nginx:1.22.1
    container_name: nginx-web
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "8083:8083"
      - "80:80"
      - "443:443"
    volumes:
      # 证书映射
      - /data/docker/nginx/cert:/etc/nginx/cert
      # 配置文件映射
      - /data/docker/nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      # 页面目录
      - /data/docker/nginx/html:/usr/share/nginx/html
      # 日志目录
      - /data/docker/nginx/log:/var/log/nginx
    privileged: true
    network_mode: "host"

  redis:
    image: redis:6.2.7
    container_name: redis
    ports:
      - "6379:6379"
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    volumes:
      # 配置文件
      - /data/docker/redis/conf:/redis/config:rw
      # 数据文件
      - /data/docker/redis/data/:/redis/data/:rw
    command: "redis-server /redis/config/redis.conf"
    privileged: true
    network_mode: "host"

  minio:
    image: minio/minio:RELEASE.2022-05-26T05-48-41Z
    container_name: minio
    ports:
      # api 端口
      - "9083:9083"
      # 控制台端口
      - "9001:9001"
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      # 管理后台用户名
      MINIO_ROOT_USER: ruoyi
      # 管理后台密码，最小8个字符
      MINIO_ROOT_PASSWORD: ruoyi123
      # https需要指定域名
      #MINIO_SERVER_URL: "https://xxx.com:9000"
      #MINIO_BROWSER_REDIRECT_URL: "https://xxx.com:9001"
      # 开启压缩 on 开启 off 关闭
      MINIO_COMPRESS: "on"
      # 扩展名 .pdf,.doc 为空 所有类型均压缩
      MINIO_COMPRESS_EXTENSIONS: ".png,.srt"
      # mime 类型 application/pdf 为空 所有类型均压缩
      MINIO_COMPRESS_MIME_TYPES: ""
    volumes:
      # 映射当前目录下的data目录至容器内/data目录
      - /data/docker/minio/data:/data
      - /data/docker/minio/log:/var/log
      # 映射配置目录
      - /data/docker/minio/config:/root/.minio/
    command: server --address ':9083' --console-address ':9001' /data # 指定容器中的目录 /data
    privileged: true
    network_mode: "host"

  yunjie-server1:
    image: yunjie/yunjie-server:4.5.0
    container_name: yunjie-server1
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      SERVER_PORT: 8080
    volumes:
      # 配置文件
      - /data/docker/server1/logs/:/ruoyi/server/logs/
      # 上传的文件
      - /data/docker/upload/:/ruoyi/server/temp/
      - /data/docker/minio/data/gis/:/gis/
      # skywalking 探针
    #      - /docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  yunjie-server2:
    image: "ruoyi/ruoyi-server:4.5.0"
    container_name: ruoyi-server2
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      SERVER_PORT: 8081
    volumes:
      # 配置文件
      - /data/docker/server2/logs/:/ruoyi/server/logs/
      # 上传的文件
      - /data/docker/upload/:/ruoyi/server/temp/
      - /data/docker/minio/data/gis/:/gis/
      # skywalking 探针
    #      - /docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-monitor-admin:
    image: ruoyi/ruoyi-monitor-admin:4.5.0
    container_name: ruoyi-monitor-admin
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    volumes:
      # 配置文件
      - /data/docker/monitor/logs/:/ruoyi/monitor/logs
    privileged: true
    network_mode: "host"

  ruoyi-xxl-job-admin:
    image: ruoyi/ruoyi-xxl-job-admin:4.5.0
    container_name: ruoyi-xxl-job-admin
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    volumes:
      # 配置文件
      - /data/docker/xxljob/logs/:/ruoyi/xxljob/logs
    privileged: true
    network_mode: "host"

#  ffmpeg:
#    image: ruoyi/ruoyi-sheet:4.5.0
#    container_name: ffmpeg
#    environment:
#      # 时区上海
#      TZ: Asia/Shanghai
#    ports:
#      - "8086:8086"
#    volumes:
#      # 配置文件
#      - /docker/ffmpeg/logs:/ruoyi/ffmpeg/logs
#      - /docker/minio/data/gis/:/ruoyi/ffmpeg/gis/
#      - /docker/ffmpeg/:/usr/bin/
#    command: -f lavfi -i testsrc -t 10 -pix_fmt yuv420p -f matroska /ruoyi/ffmpeg/gis/output.mp4
#    privileged: true
#    network_mode: "host"
