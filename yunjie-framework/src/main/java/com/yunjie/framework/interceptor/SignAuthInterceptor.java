package com.yunjie.framework.interceptor;

import com.yunjie.common.utils.sign.SignUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * C端调用接口鉴权签名
 */
@Component
public class SignAuthInterceptor implements HandlerInterceptor {

//    // 鉴权appKey
//    private static String appKey;
//
//    // 鉴权appSecret
//    private static String appSecret;
//
//    // sign失效时间，单位：秒
//    private static int expireSeconds;
//
//    @Value("${sign.appKey}")
//    public void setAppKey(String appKey) {
//        SignAuthInterceptor.appKey = appKey;
//    }
//
//    @Value("${sign.appSecret}")
//    public void setAppSecret(String appSecret) {
//        SignAuthInterceptor.appSecret = appSecret;
//    }
//
//    @Value("${sign.expireSeconds}")
//    public void setExpireSeconds(int expireSeconds) {
//        SignAuthInterceptor.expireSeconds = expireSeconds;
//    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

//        String key = request.getHeader("appKey");
//        String nonceStr = request.getHeader("nonceStr");
//        String timestamp = request.getHeader("timestamp");
//        String clientSign = request.getHeader("sign");
//
//        if (key == null || nonceStr == null || timestamp == null || clientSign == null) {
//            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "签名参数错误");
//            return false;
//        }
//
//        if (!SignUtil.isTimestampValid(timestamp, expireSeconds)) {
//            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "签名已过期");
//            return false;
//        }
//
//        Map<String, String> paramMap = new HashMap<>();
//        paramMap.put("appKey", appKey);
//        paramMap.put("nonceStr", nonceStr);
//        paramMap.put("timestamp", timestamp);
//        paramMap.put("sign", clientSign);
//
//        // 生成服务端sign签名并比较
//        // 签名方式sign=md5(appKey=xxx&nonceStr=xxx&timestamp=xxx&appSecret=xxx)
//        String serverSign = SignUtil.generateSign(paramMap, appSecret);
//        if (!serverSign.equalsIgnoreCase(clientSign)) {
//            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "无效的签名");
//            return false;
//        }

        return true;
    }
}
